<?php
/**
 * ملف الحماية الشاملة
 * يحتوي على دوال الحماية من جميع أنواع الهجمات
 */

// منع الوصول المباشر للملف
if (!defined('SECURITY_CHECK')) {
    die('Access Denied');
}

/**
 * حماية من هجمات XSS
 */
function sanitizeXSS($input) {
    if (is_array($input)) {
        return array_map('sanitizeXSS', $input);
    }
    
    // إزالة العلامات الخطيرة
    $input = strip_tags($input);
    
    // تحويل الأحرف الخاصة
    $input = htmlspecialchars($input, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // إزالة JavaScript
    $input = preg_replace('/javascript:/i', '', $input);
    $input = preg_replace('/vbscript:/i', '', $input);
    $input = preg_replace('/onload/i', '', $input);
    $input = preg_replace('/onerror/i', '', $input);
    $input = preg_replace('/onclick/i', '', $input);
    $input = preg_replace('/onmouseover/i', '', $input);
    
    return $input;
}

/**
 * حماية من هجمات SQL Injection
 */
function sanitizeSQL($input) {
    if (is_array($input)) {
        return array_map('sanitizeSQL', $input);
    }
    
    // إزالة الكلمات المحظورة
    $dangerous_words = [
        'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
        'UNION', 'OR', 'AND', 'WHERE', 'HAVING', 'ORDER', 'GROUP',
        'SCRIPT', 'EXEC', 'EXECUTE', 'DECLARE', 'CAST', 'CONVERT'
    ];
    
    foreach ($dangerous_words as $word) {
        $input = preg_replace('/\b' . $word . '\b/i', '', $input);
    }
    
    // إزالة الأحرف الخطيرة
    $input = str_replace(['--', ';', '/*', '*/', '@@', '@'], '', $input);
    
    return $input;
}

/**
 * التحقق المتقدم من صحة البريد الإلكتروني
 */
function validateEmailAdvanced($email) {
    // التحقق الأساسي
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return false;
    }

    // التحقق من الطول
    if (strlen($email) > 255) {
        return false;
    }

    // التحقق من النطاقات المحظورة
    $blocked_domains = [
        'tempmail.org', '10minutemail.com', 'guerrillamail.com',
        'mailinator.com', 'throwaway.email', 'temp-mail.org'
    ];

    $domain = substr(strrchr($email, "@"), 1);
    if (in_array(strtolower($domain), $blocked_domains)) {
        return false;
    }

    return true;
}

/**
 * التحقق من قوة كلمة المرور
 */
function validatePassword($password) {
    // الطول الأدنى
    if (strlen($password) < 8) {
        return ['valid' => false, 'message' => 'كلمة المرور قصيرة جداً'];
    }
    
    // الطول الأقصى
    if (strlen($password) > 128) {
        return ['valid' => false, 'message' => 'كلمة المرور طويلة جداً'];
    }
    
    // التحقق من وجود حرف كبير
    if (!preg_match('/[A-Z]/', $password)) {
        return ['valid' => false, 'message' => 'يجب أن تحتوي على حرف كبير'];
    }
    
    // التحقق من وجود حرف صغير
    if (!preg_match('/[a-z]/', $password)) {
        return ['valid' => false, 'message' => 'يجب أن تحتوي على حرف صغير'];
    }
    
    // التحقق من وجود رقم
    if (!preg_match('/[0-9]/', $password)) {
        return ['valid' => false, 'message' => 'يجب أن تحتوي على رقم'];
    }
    
    // التحقق من وجود رمز خاص
    if (!preg_match('/[^A-Za-z0-9]/', $password)) {
        return ['valid' => false, 'message' => 'يجب أن تحتوي على رمز خاص'];
    }
    
    // التحقق من كلمات المرور الشائعة
    $common_passwords = [
        'password', '123456', '123456789', 'qwerty', 'abc123',
        'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];
    
    if (in_array(strtolower($password), $common_passwords)) {
        return ['valid' => false, 'message' => 'كلمة المرور شائعة جداً'];
    }
    
    return ['valid' => true, 'message' => 'كلمة مرور قوية'];
}

/**
 * التحقق من عنوان IP
 */
function validateIP($ip) {
    // التحقق من صحة IP
    if (!filter_var($ip, FILTER_VALIDATE_IP)) {
        return false;
    }
    
    // التحقق من IP المحظورة
    $blocked_ips = [
        '127.0.0.1', '0.0.0.0', '::1'
    ];
    
    if (in_array($ip, $blocked_ips)) {
        return false;
    }
    
    return true;
}

/**
 * حماية من هجمات CSRF
 */
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * التحقق من User Agent
 */
function validateUserAgent($user_agent) {
    // التحقق من وجود User Agent
    if (empty($user_agent)) {
        return false;
    }
    
    // التحقق من User Agents المشبوهة
    $suspicious_agents = [
        'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget'
    ];
    
    foreach ($suspicious_agents as $agent) {
        if (stripos($user_agent, $agent) !== false) {
            return false;
        }
    }
    
    return true;
}

/**
 * حماية من هجمات Rate Limiting المتقدمة
 */
function checkAdvancedRateLimit($identifier, $max_attempts = 5, $time_window = 900) {
    $cache_key = 'rate_limit_' . md5($identifier);

    // محاولة الحصول على البيانات من الكاش
    if (function_exists('apcu_fetch')) {
        $attempts = apcu_fetch($cache_key);
        if ($attempts === false) {
            $attempts = [];
        }
    } else {
        // استخدام الجلسة كبديل
        if (!isset($_SESSION[$cache_key])) {
            $_SESSION[$cache_key] = [];
        }
        $attempts = $_SESSION[$cache_key];
    }

    $current_time = time();

    // تنظيف المحاولات القديمة
    $attempts = array_filter($attempts, function($timestamp) use ($current_time, $time_window) {
        return ($current_time - $timestamp) < $time_window;
    });

    // التحقق من عدد المحاولات
    if (count($attempts) >= $max_attempts) {
        return false;
    }
    
    // إضافة المحاولة الحالية
    $attempts[] = $current_time;

    // حفظ البيانات
    if (function_exists('apcu_store')) {
        apcu_store($cache_key, $attempts, $time_window);
    } else {
        $_SESSION[$cache_key] = $attempts;
    }

    return true;
}

/**
 * حماية من هجمات CSRF المتقدمة
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_tokens'])) {
        $_SESSION['csrf_tokens'] = [];
    }

    $token = bin2hex(random_bytes(32));
    $timestamp = time();

    // تنظيف الرموز القديمة (أكثر من 30 دقيقة)
    $_SESSION['csrf_tokens'] = array_filter($_SESSION['csrf_tokens'], function($data) {
        return (time() - $data['timestamp']) < 1800;
    });

    $_SESSION['csrf_tokens'][$token] = [
        'timestamp' => $timestamp,
        'used' => false
    ];

    return $token;
}

function validateCSRFToken($token) {
    if (!isset($_SESSION['csrf_tokens'][$token])) {
        return false;
    }

    $tokenData = $_SESSION['csrf_tokens'][$token];

    // التحقق من انتهاء صلاحية الرمز
    if ((time() - $tokenData['timestamp']) > 1800) {
        unset($_SESSION['csrf_tokens'][$token]);
        return false;
    }

    // التحقق من عدم استخدام الرمز مسبقاً
    if ($tokenData['used']) {
        return false;
    }

    // تمييز الرمز كمستخدم
    $_SESSION['csrf_tokens'][$token]['used'] = true;

    return true;
}

/**
 * حماية من هجمات File Upload
 */
function validateFileUpload($file, $allowedTypes = [], $maxSize = 5242880) {
    $errors = [];

    // التحقق من وجود الملف
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        $errors[] = 'لم يتم رفع أي ملف';
        return ['valid' => false, 'errors' => $errors];
    }

    // التحقق من حجم الملف
    if ($file['size'] > $maxSize) {
        $errors[] = 'حجم الملف كبير جداً. الحد الأقصى: ' . formatBytes($maxSize);
    }

    // التحقق من نوع الملف
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!empty($allowedTypes) && !in_array($fileExtension, $allowedTypes)) {
        $errors[] = 'نوع الملف غير مسموح. الأنواع المسموحة: ' . implode(', ', $allowedTypes);
    }

    // التحقق من MIME Type
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    $allowedMimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'webp' => 'image/webp',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'txt' => 'text/plain'
    ];

    if (isset($allowedMimes[$fileExtension]) && $mimeType !== $allowedMimes[$fileExtension]) {
        $errors[] = 'نوع الملف لا يتطابق مع امتداده';
    }

    // فحص الملف للتأكد من عدم احتوائه على كود ضار
    $fileContent = file_get_contents($file['tmp_name']);
    $maliciousPatterns = [
        '/<\?php/i',
        '/<script/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload=/i',
        '/onerror=/i'
    ];

    foreach ($maliciousPatterns as $pattern) {
        if (preg_match($pattern, $fileContent)) {
            $errors[] = 'الملف يحتوي على كود ضار';
            break;
        }
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'extension' => $fileExtension,
        'mime_type' => $mimeType,
        'size' => $file['size']
    ];
}

/**
 * حماية من هجمات Directory Traversal
 */
function sanitizePath($path) {
    // إزالة المسارات النسبية الخطيرة
    $path = str_replace(['../', '.\\', '..\\'], '', $path);

    // إزالة الأحرف الخطيرة
    $path = preg_replace('/[^a-zA-Z0-9\/_\-\.]/', '', $path);

    // إزالة المسارات المطلقة
    $path = ltrim($path, '/\\');

    return $path;
}

/**
 * حماية من هجمات Session Hijacking
 */
function regenerateSessionId() {
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_regenerate_id(true);

        // إضافة معرفات إضافية للجلسة
        $_SESSION['session_fingerprint'] = md5(
            $_SERVER['HTTP_USER_AGENT'] .
            $_SERVER['REMOTE_ADDR'] .
            session_id()
        );

        $_SESSION['session_created'] = time();
        $_SESSION['session_last_activity'] = time();
    }
}

function validateSessionFingerprint() {
    if (!isset($_SESSION['session_fingerprint'])) {
        return false;
    }

    $currentFingerprint = md5(
        $_SERVER['HTTP_USER_AGENT'] .
        $_SERVER['REMOTE_ADDR'] .
        session_id()
    );

    return $_SESSION['session_fingerprint'] === $currentFingerprint;
}

function checkSessionTimeout($timeout = 3600) {
    if (!isset($_SESSION['session_last_activity'])) {
        return false;
    }

    if ((time() - $_SESSION['session_last_activity']) > $timeout) {
        session_destroy();
        return false;
    }

    $_SESSION['session_last_activity'] = time();
    return true;
}

/**
 * حماية من هجمات Brute Force المتقدمة
 */
function checkBruteForceProtection($identifier, $action = 'login') {
    global $db;

    try {
        // تنظيف المحاولات القديمة (أكثر من ساعة)
        $stmt = $db->prepare("DELETE FROM security_attempts WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute();

        // عد المحاولات الحالية
        $stmt = $db->prepare("SELECT COUNT(*) FROM security_attempts WHERE identifier = ? AND action = ? AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)");
        $stmt->execute([$identifier, $action]);
        $attempts = $stmt->fetchColumn();

        // إذا تجاوز 5 محاولات في 15 دقيقة
        if ($attempts >= 5) {
            return false;
        }

        return true;
    } catch (PDOException $e) {
        // في حالة عدم وجود الجدول، إنشاؤه
        createSecurityTables();
        return true;
    }
}

function recordSecurityAttempt($identifier, $action = 'login', $success = false) {
    global $db;

    try {
        $stmt = $db->prepare("INSERT INTO security_attempts (identifier, action, success, ip_address, user_agent, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->execute([
            $identifier,
            $action,
            $success ? 1 : 0,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (PDOException $e) {
        // تجاهل الخطأ إذا لم يكن الجدول موجود
    }
}

/**
 * إنشاء جداول الأمان إذا لم تكن موجودة
 */
function createSecurityTables() {
    global $db;

    try {
        // جدول محاولات الأمان
        $db->exec("CREATE TABLE IF NOT EXISTS security_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            identifier VARCHAR(255) NOT NULL,
            action VARCHAR(50) NOT NULL,
            success BOOLEAN DEFAULT FALSE,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_identifier_action (identifier, action),
            INDEX idx_created_at (created_at)
        )");

        // جدول سجل الأحداث الأمنية
        $db->exec("CREATE TABLE IF NOT EXISTS security_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_type VARCHAR(100) NOT NULL,
            user_id INT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            event_data JSON,
            severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_type (event_type),
            INDEX idx_user_id (user_id),
            INDEX idx_created_at (created_at),
            INDEX idx_severity (severity)
        )");

        // جدول IP المحظورة
        $db->exec("CREATE TABLE IF NOT EXISTS blocked_ips (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ip_address VARCHAR(45) NOT NULL UNIQUE,
            reason TEXT,
            blocked_by INT,
            blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NULL,
            is_permanent BOOLEAN DEFAULT FALSE,
            INDEX idx_ip_address (ip_address),
            INDEX idx_expires_at (expires_at)
        )");

    } catch (PDOException $e) {
        // تجاهل الأخطاء
    }
}

/**
 * تسجيل الأحداث الأمنية
 */
function logSecurityEvent($eventType, $eventData = [], $severity = 'medium') {
    global $db;

    try {
        $stmt = $db->prepare("INSERT INTO security_logs (event_type, user_id, ip_address, user_agent, event_data, severity, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $stmt->execute([
            $eventType,
            $_SESSION['user_id'] ?? null,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            json_encode($eventData),
            $severity
        ]);
    } catch (PDOException $e) {
        // في حالة عدم وجود الجدول، إنشاؤه
        createSecurityTables();

        // محاولة مرة أخرى
        try {
            $stmt = $db->prepare("INSERT INTO security_logs (event_type, user_id, ip_address, user_agent, event_data, severity, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                $eventType,
                $_SESSION['user_id'] ?? null,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? '',
                json_encode($eventData),
                $severity
            ]);
        } catch (PDOException $e) {
            // تجاهل الخطأ
        }
    }
}

/**
 * فحص IP المحظورة
 */
function checkBlockedIP($ipAddress = null) {
    global $db;

    if (!$ipAddress) {
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
    }

    try {
        $stmt = $db->prepare("SELECT * FROM blocked_ips WHERE ip_address = ? AND (is_permanent = 1 OR expires_at > NOW())");
        $stmt->execute([$ipAddress]);
        $blocked = $stmt->fetch();

        if ($blocked) {
            logSecurityEvent('blocked_ip_access_attempt', [
                'ip_address' => $ipAddress,
                'reason' => $blocked['reason']
            ], 'high');

            return false;
        }

        return true;
    } catch (PDOException $e) {
        return true; // في حالة عدم وجود الجدول، السماح بالوصول
    }
}

/**
 * حظر IP
 */
function blockIP($ipAddress, $reason = '', $duration = 3600) {
    global $db;

    try {
        $expiresAt = $duration > 0 ? date('Y-m-d H:i:s', time() + $duration) : null;
        $isPermanent = $duration <= 0;

        $stmt = $db->prepare("INSERT INTO blocked_ips (ip_address, reason, blocked_by, expires_at, is_permanent) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE reason = VALUES(reason), expires_at = VALUES(expires_at), is_permanent = VALUES(is_permanent)");
        $stmt->execute([
            $ipAddress,
            $reason,
            $_SESSION['user_id'] ?? null,
            $expiresAt,
            $isPermanent
        ]);

        logSecurityEvent('ip_blocked', [
            'ip_address' => $ipAddress,
            'reason' => $reason,
            'duration' => $duration,
            'is_permanent' => $isPermanent
        ], 'high');

        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * تنسيق حجم الملف
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }

    return round($size, $precision) . ' ' . $units[$i];
}





/**
 * التحقق من صحة رقم الهاتف
 */
function validatePhone($phone) {
    // إزالة المسافات والأحرف غير الرقمية
    $phone = preg_replace('/[^0-9+]/', '', $phone);

    // التحقق من الطول
    if (strlen($phone) < 10 || strlen($phone) > 15) {
        return false;
    }

    // التحقق من النمط
    if (!preg_match('/^\+?[0-9]{10,15}$/', $phone)) {
        return false;
    }

    return true;
}

/**
 * التحقق من صحة رقم الحساب البنكي
 */
function validateBankAccount($account) {
    // إزالة المسافات والأحرف غير الرقمية
    $account = preg_replace('/[^0-9]/', '', $account);

    // التحقق من الطول (24 رقم بالضبط)
    if (strlen($account) !== 24) {
        return false;
    }

    // التحقق من النمط (أرقام فقط)
    if (!preg_match('/^[0-9]{24}$/', $account)) {
        return false;
    }

    return true;
}
?>
