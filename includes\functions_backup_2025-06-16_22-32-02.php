<?php
// ملف الوظائف المساعدة
// تعريف ثابت الحماية
define('SECURITY_CHECK', true);

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/security.php';
require_once __DIR__ . '/advanced_security.php';
require_once __DIR__ . '/simple_csrf.php';

// وظيفة الحصول على اتصال قاعدة البيانات
function getDBConnection() {
    global $db;
    return $db;
}

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// وظيفة تشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// وظيفة التحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// وظيفة تنظيف البيانات
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// وظيفة تنظيف البيانات المتقدمة
function sanitizeInputAdvanced($input) {
    if (is_array($input)) {
        return array_map('sanitizeInputAdvanced', $input);
    }

    // إزالة المسافات الزائدة
    $input = trim($input);

    // إزالة الشرطات المائلة
    $input = stripslashes($input);

    // استخدام دوال الحماية المتقدمة
    $input = sanitizeXSS($input);
    $input = sanitizeSQL($input);

    return $input;
}

// وظيفة التحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// وظيفة إنشاء رمز جلسة عشوائي
function generateSessionToken() {
    return bin2hex(random_bytes(32));
}

// وظيفة التحقق من تسجيل دخول المستخدم
function isUserLoggedIn() {
    if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
        return false;
    }

    return true;
}

// وظيفة التحقق من تسجيل دخول البائع
function isSellerLoggedIn() {
    return isUserLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'seller';
}

// وظيفة التحقق من تسجيل دخول المدير
function isAdminLoggedIn() {
    return isUserLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// وظيفة الحصول على معلومات المستخدم الحالي
function getCurrentUser() {
    global $db;

    if (!isUserLoggedIn()) {
        return false;
    }

    try {
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        return false;
    }
}

// وظيفة الحصول على معلومات البائع الحالي (للتوافق مع الكود القديم)
function getCurrentSeller() {
    return getCurrentUser();
}

// وظيفة تسجيل خروج المستخدم
function logoutUser() {
    global $db;

    if (isset($_SESSION['user_id'])) {
        // حذف رمز الجلسة من قاعدة البيانات
        try {
            $stmt = $db->prepare("DELETE FROM user_sessions WHERE user_id = ?");
            $stmt->execute([$_SESSION['user_id']]);
        } catch (PDOException $e) {
            // تجاهل الخطأ
        }
    }

    // مسح جميع متغيرات الجلسة
    $_SESSION = array();

    // حذف ملف تعريف الارتباط للجلسة
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // تدمير الجلسة
    session_destroy();
}

// وظيفة تسجيل خروج البائع (للتوافق مع الكود القديم)
function logoutSeller() {
    logoutUser();
}

// وظيفة إعادة التوجيه
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// وظيفة التحقق من الصلاحيات
function requireLogin() {
    if (!isUserLoggedIn()) {
        redirect('../pages/login.php');
    }
}

function requireAdmin() {
    requireLogin();
    if (!isAdminLoggedIn()) {
        redirect('../pages/dashboard.php');
    }
}

function requireSeller() {
    requireLogin();
    if (!isSellerLoggedIn()) {
        redirect('../admin/index.php');
    }
}

// وظيفة التحقق من ملكية البيانات للبائع
function checkSellerOwnership($sellerId) {
    if (!isSellerLoggedIn() || $_SESSION['user_id'] != $sellerId) {
        redirect('../pages/dashboard.php');
    }
}

// وظيفة عرض الرسائل
function showMessage($message, $type = 'info') {
    $alertClass = '';
    switch ($type) {
        case 'success':
            $alertClass = 'alert-success';
            break;
        case 'error':
            $alertClass = 'alert-danger';
            break;
        case 'warning':
            $alertClass = 'alert-warning';
            break;
        default:
            $alertClass = 'alert-info';
    }
    
    return '<div class="alert ' . $alertClass . ' alert-dismissible fade show" role="alert">
                ' . htmlspecialchars($message) . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
}

// وظيفة تنسيق السعر
function formatPrice($price) {
    return number_format($price, 2) . ' د.م';
}

// وظيفة تنسيق التاريخ
function formatDate($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// وظيفة إنشاء رقم طلب فريد
function generateOrderNumber() {
    return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

// وظيفة حساب هامش الربح
function calculateProfitMargin($sellingPrice, $supplierPrice) {
    if ($supplierPrice <= 0) return 0;
    return (($sellingPrice - $supplierPrice) / $supplierPrice) * 100;
}

// وظيفة التحقق من حالة المنتج في المخزون
function getStockStatusText($status) {
    switch ($status) {
        case 'in_stock':
            return 'متوفر';
        case 'out_of_stock':
            return 'غير متوفر';
        case 'limited':
            return 'كمية محدودة';
        default:
            return 'غير محدد';
    }
}

// وظيفة التحقق من حالة الطلب
function getOrderStatusText($status) {
    switch ($status) {
        case 'pending':
            return 'في الانتظار';
        case 'confirmed':
            return 'مؤكد';
        case 'processing':
            return 'قيد المعالجة';
        case 'shipped':
            return 'تم الشحن';
        case 'delivered':
            return 'تم التسليم';
        case 'cancelled':
            return 'ملغي';
        default:
            return 'غير محدد';
    }
}

// وظيفة التحقق من حالة الدفع
function getPaymentStatusText($status) {
    switch ($status) {
        case 'pending':
            return 'في الانتظار';
        case 'paid':
            return 'مدفوع';
        case 'failed':
            return 'فشل';
        case 'refunded':
            return 'مسترد';
        default:
            return 'غير محدد';
    }
}

// وظيفة رفع الصور
function uploadImage($file, $targetDir = 'assets/images/uploads/') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }

    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        return false;
    }

    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $targetPath = $targetDir . $filename;

    if (!is_dir($targetDir)) {
        mkdir($targetDir, 0755, true);
    }

    if (move_uploaded_file($file['tmp_name'], $targetPath)) {
        return $filename;
    }

    return false;
}

// وظيفة إنشاء رقم فاتورة فريد
function generateInvoiceNumber() {
    return 'INV-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

// وظيفة الحصول على إحصائيات المدير
function getAdminStats() {
    global $db;

    try {
        $stats = [];

        // عدد البائعين
        $stmt = $db->prepare("SELECT COUNT(*) as total,
                             SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
                             SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
                             FROM users WHERE role = 'seller'");
        $stmt->execute();
        $stats['sellers'] = $stmt->fetch();

        // عدد المنتجات
        $stmt = $db->prepare("SELECT COUNT(*) as total,
                             SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active
                             FROM products");
        $stmt->execute();
        $stats['products'] = $stmt->fetch();

        // عدد الطلبات مع جميع الحالات
        $stmt = $db->prepare("SELECT
                             COUNT(*) as total,
                             SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                             SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
                             SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                             SUM(CASE WHEN status = 'shipped' THEN 1 ELSE 0 END) as shipped,
                             SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                             SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                             SUM(CASE WHEN status = 'returned' THEN 1 ELSE 0 END) as returned,
                             SUM(CASE WHEN status = 'delivered' THEN total_amount ELSE 0 END) as total_sales,
                             SUM(total_amount) as total_revenue,
                             AVG(total_amount) as avg_order_value
                             FROM orders");
        $stmt->execute();
        $stats['orders'] = $stmt->fetch();

        // إحصائيات الطلبات اليومية
        $stmt = $db->prepare("SELECT
                             COUNT(*) as today_orders,
                             SUM(total_amount) as today_sales
                             FROM orders
                             WHERE DATE(created_at) = CURDATE()");
        $stmt->execute();
        $stats['orders_today'] = $stmt->fetch();

        // إحصائيات الطلبات الأسبوعية
        $stmt = $db->prepare("SELECT
                             COUNT(*) as week_orders,
                             SUM(total_amount) as week_sales
                             FROM orders
                             WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
        $stmt->execute();
        $stats['orders_week'] = $stmt->fetch();

        // إحصائيات الطلبات الشهرية
        $stmt = $db->prepare("SELECT
                             COUNT(*) as month_orders,
                             SUM(total_amount) as month_sales
                             FROM orders
                             WHERE MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())");
        $stmt->execute();
        $stats['orders_month'] = $stmt->fetch();

        // حساب الأرباح من الطلبات المسلمة فقط
        $stmt = $db->prepare("SELECT
                             SUM(o.total_amount) as total_delivered_sales,
                             COALESCE(SUM(o.admin_commission), SUM(o.commission_amount)) as admin_total_profit,
                             COALESCE(SUM(o.seller_profit), SUM(o.total_amount - o.commission_amount)) as sellers_total_profit,
                             COALESCE(SUM(o.admin_commission + o.seller_profit), SUM(o.total_amount)) as total_profit,
                             COUNT(*) as delivered_orders_count
                             FROM orders o
                             WHERE o.status = 'delivered'");
        $stmt->execute();
        $stats['profits'] = $stmt->fetch();

        // أرباح اليوم من الطلبات المسلمة
        $stmt = $db->prepare("SELECT
                             SUM(o.total_amount) as today_delivered_sales,
                             COALESCE(SUM(o.admin_commission), SUM(o.commission_amount)) as admin_today_profit,
                             COALESCE(SUM(o.seller_profit), SUM(o.total_amount - o.commission_amount)) as sellers_today_profit
                             FROM orders o
                             WHERE o.status = 'delivered' AND DATE(o.updated_at) = CURDATE()");
        $stmt->execute();
        $stats['profits_today'] = $stmt->fetch();

        // أرباح الشهر من الطلبات المسلمة
        $stmt = $db->prepare("SELECT
                             SUM(o.total_amount) as month_delivered_sales,
                             COALESCE(SUM(o.admin_commission), SUM(o.commission_amount)) as admin_month_profit,
                             COALESCE(SUM(o.seller_profit), SUM(o.total_amount - o.commission_amount)) as sellers_month_profit
                             FROM orders o
                             WHERE o.status = 'delivered'
                             AND MONTH(o.updated_at) = MONTH(NOW())
                             AND YEAR(o.updated_at) = YEAR(NOW())");
        $stmt->execute();
        $stats['profits_month'] = $stmt->fetch();

        // أفضل 5 منتجات طلباً
        $stmt = $db->prepare("SELECT
                             p.name as product_name,
                             p.category,
                             p.price,
                             COUNT(oi.id) as order_count,
                             SUM(oi.quantity) as total_quantity,
                             SUM(oi.price * oi.quantity) as total_revenue
                             FROM products p
                             LEFT JOIN order_items oi ON p.id = oi.product_id
                             LEFT JOIN orders o ON oi.order_id = o.id
                             WHERE o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                             GROUP BY p.id, p.name, p.category, p.price
                             HAVING order_count > 0
                             ORDER BY order_count DESC, total_quantity DESC
                             LIMIT 5");
        $stmt->execute();
        $stats['top_products'] = $stmt->fetchAll();

        // أفضل 10 بائعين حسب عدد الطلبات
        $stmt = $db->prepare("SELECT
                             u.id as seller_id,
                             u.full_name,
                             u.store_name,
                             u.username,
                             COUNT(o.id) as total_orders,
                             COUNT(CASE WHEN o.status = 'delivered' THEN 1 END) as delivered_orders,
                             COUNT(CASE WHEN o.status = 'pending' THEN 1 END) as pending_orders,
                             SUM(o.total_amount) as total_sales,
                             SUM(CASE WHEN o.status = 'delivered' THEN o.total_amount ELSE 0 END) as delivered_sales,
                             AVG(o.total_amount) as avg_order_value,
                             MAX(o.created_at) as last_order_date
                             FROM users u
                             LEFT JOIN orders o ON u.id = o.seller_id
                             WHERE u.role = 'seller' AND u.status = 'active'
                             AND o.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                             GROUP BY u.id, u.full_name, u.store_name, u.username
                             HAVING total_orders > 0
                             ORDER BY total_orders DESC, delivered_orders DESC
                             LIMIT 10");
        $stmt->execute();
        $stats['top_sellers'] = $stmt->fetchAll();

        // عدد الفواتير
        $stmt = $db->prepare("SELECT COUNT(*) as total,
                             SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                             SUM(amount) as total_amount
                             FROM invoices");
        $stmt->execute();
        $stats['invoices'] = $stmt->fetch();

        return $stats;
    } catch (PDOException $e) {
        return false;
    }
}

// وظيفة الحصول على إحصائيات البائع
function getSellerStats($sellerId) {
    global $db;

    try {
        $stats = [];

        // منتجات البائع
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM seller_products WHERE seller_id = ?");
        $stmt->execute([$sellerId]);
        $stats['products'] = $stmt->fetch()['total'];

        // طلبات البائع
        $stmt = $db->prepare("SELECT COUNT(*) as total,
                             SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                             SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as completed,
                             SUM(total_amount) as total_sales,
                             SUM(commission_amount) as total_commission
                             FROM orders WHERE seller_id = ?");
        $stmt->execute([$sellerId]);
        $stats['orders'] = $stmt->fetch();

        // فواتير البائع
        $stmt = $db->prepare("SELECT COUNT(*) as total,
                             SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                             SUM(amount) as total_amount
                             FROM invoices WHERE seller_id = ?");
        $stmt->execute([$sellerId]);
        $stats['invoices'] = $stmt->fetch();

        return $stats;
    } catch (PDOException $e) {
        return false;
    }
}

// وظيفة التحقق من حالة المستخدم
function getUserStatusText($status) {
    switch ($status) {
        case 'active':
            return 'نشط';
        case 'pending':
            return 'في الانتظار';
        case 'suspended':
            return 'معلق';
        default:
            return 'غير محدد';
    }
}

// وظيفة التحقق من نوع الفاتورة
function getInvoiceTypeText($type) {
    switch ($type) {
        case 'commission':
            return 'عمولة';
        case 'payout':
            return 'دفعة';
        case 'other':
            return 'أخرى';
        default:
            return 'غير محدد';
    }
}

// وظيفة التحقق من حالة الفاتورة
function getInvoiceStatusText($status) {
    switch ($status) {
        case 'pending':
            return 'في الانتظار';
        case 'paid':
            return 'مدفوعة';
        case 'cancelled':
            return 'ملغاة';
        default:
            return 'غير محدد';
    }
}
?>
