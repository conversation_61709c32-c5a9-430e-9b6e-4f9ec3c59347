-- ===================================================================
-- قاعدة بيانات مشروع ProGet - منصة التجارة الإلكترونية
-- تاريخ الإنشاء: 2024-06-16
-- الوصف: قاعدة بيانات شاملة لمنصة تجارة إلكترونية بنظام البائعين المتعددين
-- ===================================================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS proget_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE proget_db;

-- ===================================================================
-- جدول المستخدمين (البائعين والمديرين والعملاء)
-- ===================================================================
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    whatsapp VARCHAR(20),
    role ENUM('admin', 'seller', 'customer') NOT NULL DEFAULT 'customer',
    status ENUM('active', 'inactive', 'pending') NOT NULL DEFAULT 'pending',
    
    -- معلومات البائع
    store_name VARCHAR(100),
    store_description TEXT,
    bank_name VARCHAR(100),
    bank_account VARCHAR(24), -- RIB المغربي 24 رقم
    commission_rate DECIMAL(5,2) DEFAULT 5.00, -- نسبة العمولة
    
    -- معلومات العنوان
    address TEXT,
    city VARCHAR(50),
    postal_code VARCHAR(10),
    country VARCHAR(50) DEFAULT 'المغرب',
    
    -- معلومات إضافية
    avatar VARCHAR(255),
    birth_date DATE,
    gender ENUM('male', 'female'),
    
    -- تواريخ النظام
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    email_verified_at TIMESTAMP NULL,
    
    -- فهارس
    INDEX idx_email (email),
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_city (city),
    INDEX idx_created_at (created_at)
);

-- ===================================================================
-- جدول الفئات
-- ===================================================================
CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    description TEXT,
    parent_id INT NULL,
    image VARCHAR(255),
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    meta_title VARCHAR(255),
    meta_description TEXT,
    slug VARCHAR(100) UNIQUE,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_parent_id (parent_id),
    INDEX idx_slug (slug),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
);

-- ===================================================================
-- جدول المنتجات
-- ===================================================================
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    seller_id INT NOT NULL,
    category_id INT NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    
    -- معلومات السعر
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2),
    cost_price DECIMAL(10,2),
    
    -- معلومات المخزون
    sku VARCHAR(100) UNIQUE,
    stock_quantity INT DEFAULT 0,
    min_stock_level INT DEFAULT 5,
    manage_stock BOOLEAN DEFAULT TRUE,
    stock_status ENUM('in_stock', 'out_of_stock', 'on_backorder') DEFAULT 'in_stock',
    
    -- معلومات الشحن
    weight DECIMAL(8,2),
    length DECIMAL(8,2),
    width DECIMAL(8,2),
    height DECIMAL(8,2),
    shipping_class VARCHAR(50),
    
    -- معلومات المنتج
    status ENUM('active', 'inactive', 'pending', 'rejected') DEFAULT 'pending',
    featured BOOLEAN DEFAULT FALSE,
    virtual BOOLEAN DEFAULT FALSE,
    downloadable BOOLEAN DEFAULT FALSE,
    
    -- SEO
    meta_title VARCHAR(255),
    meta_description TEXT,
    slug VARCHAR(200) UNIQUE,
    
    -- إحصائيات
    views_count INT DEFAULT 0,
    sales_count INT DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    
    INDEX idx_seller_id (seller_id),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_featured (featured),
    INDEX idx_price (price),
    INDEX idx_stock_status (stock_status),
    INDEX idx_slug (slug),
    INDEX idx_sku (sku),
    INDEX idx_created_at (created_at),
    
    FULLTEXT idx_search (name, description, short_description)
);

-- ===================================================================
-- جدول صور المنتجات
-- ===================================================================
CREATE TABLE product_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    image_path VARCHAR(255) NOT NULL,
    alt_text VARCHAR(255),
    is_primary BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_is_primary (is_primary),
    INDEX idx_sort_order (sort_order)
);

-- ===================================================================
-- جدول خصائص المنتجات
-- ===================================================================
CREATE TABLE product_attributes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    attribute_name VARCHAR(100) NOT NULL,
    attribute_value VARCHAR(255) NOT NULL,
    sort_order INT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_attribute_name (attribute_name)
);

-- ===================================================================
-- جدول الطلبات
-- ===================================================================
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT,
    seller_id INT NOT NULL,
    
    -- معلومات العميل
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(100),
    customer_phone VARCHAR(20) NOT NULL,
    customer_whatsapp VARCHAR(20),
    
    -- معلومات الشحن
    shipping_address TEXT NOT NULL,
    shipping_city VARCHAR(50) NOT NULL,
    shipping_postal_code VARCHAR(10),
    shipping_country VARCHAR(50) DEFAULT 'المغرب',
    
    -- معلومات الفواتير
    billing_address TEXT,
    billing_city VARCHAR(50),
    billing_postal_code VARCHAR(10),
    billing_country VARCHAR(50) DEFAULT 'المغرب',
    
    -- معلومات المالية
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    shipping_amount DECIMAL(10,2) DEFAULT 0.00,
    discount_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    
    -- العمولات
    commission_rate DECIMAL(5,2) DEFAULT 5.00,
    commission_amount DECIMAL(10,2),
    admin_commission DECIMAL(10,2),
    seller_profit DECIMAL(10,2),
    
    -- معلومات الطلب
    status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned', 'refunded') DEFAULT 'pending',
    payment_method ENUM('cash_on_delivery', 'bank_transfer', 'credit_card', 'mobile_payment') DEFAULT 'cash_on_delivery',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    
    -- معلومات الشحن
    shipping_method VARCHAR(100),
    tracking_number VARCHAR(100),
    shipped_at TIMESTAMP NULL,
    delivered_at TIMESTAMP NULL,
    
    -- ملاحظات
    customer_notes TEXT,
    admin_notes TEXT,
    seller_notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE RESTRICT,
    
    INDEX idx_order_number (order_number),
    INDEX idx_customer_id (customer_id),
    INDEX idx_seller_id (seller_id),
    INDEX idx_status (status),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at),
    INDEX idx_customer_phone (customer_phone),
    INDEX idx_shipping_city (shipping_city)
);

-- ===================================================================
-- جدول عناصر الطلبات
-- ===================================================================
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    product_name VARCHAR(200) NOT NULL,
    product_sku VARCHAR(100),
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    total DECIMAL(10,2) NOT NULL,

    -- معلومات المنتج وقت الطلب
    product_image VARCHAR(255),
    product_attributes JSON,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT,

    INDEX idx_order_id (order_id),
    INDEX idx_product_id (product_id)
);

-- ===================================================================
-- جدول تتبع حالة الطلبات
-- ===================================================================
CREATE TABLE order_status_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned', 'refunded') NOT NULL,
    notes TEXT,
    changed_by INT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_order_id (order_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- ===================================================================
-- جدول سلة التسوق
-- ===================================================================
CREATE TABLE cart (
    id INT AUTO_INCREMENT PRIMARY KEY,
    session_id VARCHAR(255),
    customer_id INT,
    product_id INT NOT NULL,
    quantity INT NOT NULL DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,

    INDEX idx_session_id (session_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_product_id (product_id),

    UNIQUE KEY unique_cart_item (session_id, customer_id, product_id)
);

-- ===================================================================
-- جدول قائمة الأمنيات
-- ===================================================================
CREATE TABLE wishlist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    product_id INT NOT NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,

    INDEX idx_customer_id (customer_id),
    INDEX idx_product_id (product_id),

    UNIQUE KEY unique_wishlist_item (customer_id, product_id)
);

-- ===================================================================
-- جدول التقييمات والمراجعات
-- ===================================================================
CREATE TABLE reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT NOT NULL,
    customer_id INT,
    order_id INT,

    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(200),
    comment TEXT,

    -- معلومات العميل (في حالة عدم التسجيل)
    customer_name VARCHAR(100),
    customer_email VARCHAR(100),

    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    is_verified_purchase BOOLEAN DEFAULT FALSE,

    helpful_count INT DEFAULT 0,
    not_helpful_count INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,

    INDEX idx_product_id (product_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_rating (rating),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- ===================================================================
-- جدول كوبونات الخصم
-- ===================================================================
CREATE TABLE coupons (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,

    -- نوع الخصم
    discount_type ENUM('percentage', 'fixed_amount') NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,

    -- شروط الاستخدام
    minimum_amount DECIMAL(10,2) DEFAULT 0.00,
    maximum_discount DECIMAL(10,2),
    usage_limit INT,
    usage_limit_per_customer INT DEFAULT 1,
    used_count INT DEFAULT 0,

    -- صلاحية الكوبون
    start_date TIMESTAMP,
    end_date TIMESTAMP,

    -- قيود الاستخدام
    applicable_products JSON, -- قائمة معرفات المنتجات
    applicable_categories JSON, -- قائمة معرفات الفئات
    excluded_products JSON,
    excluded_categories JSON,

    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date)
);

-- ===================================================================
-- جدول استخدام الكوبونات
-- ===================================================================
CREATE TABLE coupon_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    coupon_id INT NOT NULL,
    order_id INT NOT NULL,
    customer_id INT,
    discount_amount DECIMAL(10,2) NOT NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (coupon_id) REFERENCES coupons(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_coupon_id (coupon_id),
    INDEX idx_order_id (order_id),
    INDEX idx_customer_id (customer_id)
);

-- ===================================================================
-- جدول الإشعارات
-- ===================================================================
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    type ENUM('order', 'product', 'system', 'promotion', 'review') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSON,
    related_id INT,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read)
);

-- ===================================================================
-- جدول الإعدادات
-- ===================================================================
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value LONGTEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string',
    description TEXT,
    group_name VARCHAR(50) DEFAULT 'general',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_setting_key (setting_key),
    INDEX idx_group_name (group_name)
);

-- ===================================================================
-- جدول سجل النشاطات
-- ===================================================================
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    model_type VARCHAR(100),
    model_id INT,
    properties JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- ===================================================================
-- إدراج البيانات الأساسية
-- ===================================================================

-- إنشاء حساب المدير الرئيسي
INSERT INTO users (
    username,
    email,
    password,
    full_name,
    phone,
    whatsapp,
    role,
    status,
    city,
    country,
    created_at,
    email_verified_at
) VALUES (
    'admin',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'مدير النظام',
    '+212 6 12 34 56 78',
    '+212 6 12 34 56 78',
    'admin',
    'active',
    'الدار البيضاء',
    'المغرب',
    NOW(),
    NOW()
);

-- إدراج الفئات الأساسية
INSERT INTO categories (name, name_en, description, slug, sort_order, is_active) VALUES
('الإلكترونيات', 'Electronics', 'أجهزة إلكترونية ومعدات تقنية', 'electronics', 1, TRUE),
('الأزياء والموضة', 'Fashion', 'ملابس وإكسسوارات للرجال والنساء', 'fashion', 2, TRUE),
('المنزل والحديقة', 'Home & Garden', 'أثاث ومستلزمات منزلية', 'home-garden', 3, TRUE),
('الصحة والجمال', 'Health & Beauty', 'منتجات العناية والجمال', 'health-beauty', 4, TRUE),
('الرياضة واللياقة', 'Sports & Fitness', 'معدات رياضية ولياقة بدنية', 'sports-fitness', 5, TRUE),
('الكتب والتعليم', 'Books & Education', 'كتب ومواد تعليمية', 'books-education', 6, TRUE),
('الألعاب والترفيه', 'Games & Entertainment', 'ألعاب وأدوات ترفيه', 'games-entertainment', 7, TRUE),
('السيارات والمركبات', 'Automotive', 'قطع غيار ومستلزمات السيارات', 'automotive', 8, TRUE);

-- إدراج الفئات الفرعية للإلكترونيات
INSERT INTO categories (name, name_en, description, parent_id, slug, sort_order, is_active) VALUES
('الهواتف الذكية', 'Smartphones', 'هواتف ذكية وملحقاتها', 1, 'smartphones', 1, TRUE),
('أجهزة الكمبيوتر', 'Computers', 'أجهزة كمبيوتر ولابتوب', 1, 'computers', 2, TRUE),
('الأجهزة المنزلية', 'Home Appliances', 'أجهزة كهربائية منزلية', 1, 'home-appliances', 3, TRUE),
('الكاميرات والتصوير', 'Cameras', 'كاميرات ومعدات تصوير', 1, 'cameras', 4, TRUE);

-- إدراج الفئات الفرعية للأزياء
INSERT INTO categories (name, name_en, description, parent_id, slug, sort_order, is_active) VALUES
('ملابس رجالية', 'Men Clothing', 'ملابس للرجال', 2, 'men-clothing', 1, TRUE),
('ملابس نسائية', 'Women Clothing', 'ملابس للنساء', 2, 'women-clothing', 2, TRUE),
('أحذية', 'Shoes', 'أحذية رجالية ونسائية', 2, 'shoes', 3, TRUE),
('حقائب وإكسسوارات', 'Bags & Accessories', 'حقائب وإكسسوارات', 2, 'bags-accessories', 4, TRUE);

-- إدراج الإعدادات الأساسية
INSERT INTO settings (setting_key, setting_value, setting_type, description, group_name) VALUES
('site_name', 'ProGet - منصة التجارة الإلكترونية', 'string', 'اسم الموقع', 'general'),
('site_description', 'منصة تجارة إلكترونية متقدمة للبائعين المتعددين', 'text', 'وصف الموقع', 'general'),
('site_email', '<EMAIL>', 'string', 'البريد الإلكتروني للموقع', 'general'),
('site_phone', '+212 5 22 12 34 56', 'string', 'رقم هاتف الموقع', 'general'),
('site_address', 'الدار البيضاء، المغرب', 'string', 'عنوان الموقع', 'general'),
('default_currency', 'MAD', 'string', 'العملة الافتراضية', 'general'),
('currency_symbol', 'د.م', 'string', 'رمز العملة', 'general'),
('default_commission_rate', '5.00', 'number', 'نسبة العمولة الافتراضية', 'commission'),
('min_order_amount', '50.00', 'number', 'أقل مبلغ للطلب', 'orders'),
('max_order_amount', '10000.00', 'number', 'أعلى مبلغ للطلب', 'orders'),
('enable_reviews', 'true', 'boolean', 'تفعيل نظام التقييمات', 'features'),
('enable_wishlist', 'true', 'boolean', 'تفعيل قائمة الأمنيات', 'features'),
('enable_coupons', 'true', 'boolean', 'تفعيل نظام الكوبونات', 'features'),
('auto_approve_products', 'false', 'boolean', 'الموافقة التلقائية على المنتجات', 'products'),
('auto_approve_sellers', 'false', 'boolean', 'الموافقة التلقائية على البائعين', 'sellers'),
('email_notifications', 'true', 'boolean', 'تفعيل الإشعارات بالبريد الإلكتروني', 'notifications'),
('sms_notifications', 'false', 'boolean', 'تفعيل الإشعارات بالرسائل النصية', 'notifications');

-- إنشاء بائع تجريبي
INSERT INTO users (
    username,
    email,
    password,
    full_name,
    phone,
    whatsapp,
    role,
    status,
    store_name,
    store_description,
    bank_name,
    bank_account,
    commission_rate,
    city,
    country,
    created_at
) VALUES (
    'seller_demo',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'أحمد محمد التجاري',
    '+212 6 87 65 43 21',
    '+212 6 87 65 43 21',
    'seller',
    'active',
    'متجر الإلكترونيات الحديثة',
    'متجر متخصص في بيع الأجهزة الإلكترونية والهواتف الذكية',
    'البنك الشعبي',
    '123456789012345678901234',
    5.00,
    'الرباط',
    'المغرب',
    NOW()
);

-- إنشاء عميل تجريبي
INSERT INTO users (
    username,
    email,
    password,
    full_name,
    phone,
    whatsapp,
    role,
    status,
    address,
    city,
    country,
    created_at
) VALUES (
    'customer_demo',
    '<EMAIL>',
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
    'فاطمة الزهراء العميلة',
    '+212 6 11 22 33 44',
    '+212 6 11 22 33 44',
    'customer',
    'active',
    'شارع محمد الخامس، حي النهضة',
    'الدار البيضاء',
    'المغرب',
    NOW()
);

-- إدراج منتجات تجريبية
INSERT INTO products (
    seller_id,
    category_id,
    name,
    description,
    short_description,
    price,
    sale_price,
    sku,
    stock_quantity,
    status,
    featured,
    slug,
    created_at
) VALUES
(2, 9, 'آيفون 15 برو ماكس', 'هاتف ذكي متطور من آبل بمواصفات عالية وكاميرا احترافية', 'آيفون 15 برو ماكس - 256 جيجا', 15999.00, 14999.00, 'IPHONE15PM256', 10, 'active', TRUE, 'iphone-15-pro-max', NOW()),
(2, 9, 'سامسونج جالاكسي S24 الترا', 'هاتف ذكي من سامسونج بتقنيات متقدمة', 'جالاكسي S24 الترا - 512 جيجا', 12999.00, NULL, 'GALAXY-S24-ULTRA', 15, 'active', TRUE, 'samsung-galaxy-s24-ultra', NOW()),
(2, 10, 'لابتوب ديل XPS 13', 'لابتوب عالي الأداء للمحترفين', 'ديل XPS 13 - معالج i7 - 16 جيجا رام', 18999.00, 17999.00, 'DELL-XPS13-I7', 5, 'active', FALSE, 'dell-xps-13', NOW()),
(2, 10, 'ماك بوك آير M2', 'لابتوب آبل بمعالج M2 الجديد', 'ماك بوك آير - معالج M2 - 256 جيجا', 16999.00, NULL, 'MACBOOK-AIR-M2', 8, 'active', TRUE, 'macbook-air-m2', NOW()),
(2, 12, 'كاميرا كانون EOS R6', 'كاميرا احترافية للتصوير الفوتوغرافي', 'كانون EOS R6 - كاميرا ميرورليس', 25999.00, 24999.00, 'CANON-EOS-R6', 3, 'active', FALSE, 'canon-eos-r6', NOW());

-- إدراج صور المنتجات
INSERT INTO product_images (product_id, image_path, alt_text, is_primary, sort_order) VALUES
(1, '/uploads/products/iphone15pm_1.jpg', 'آيفون 15 برو ماكس - الصورة الرئيسية', TRUE, 1),
(1, '/uploads/products/iphone15pm_2.jpg', 'آيفون 15 برو ماكس - من الخلف', FALSE, 2),
(2, '/uploads/products/galaxy_s24_1.jpg', 'سامسونج جالاكسي S24 الترا', TRUE, 1),
(3, '/uploads/products/dell_xps13_1.jpg', 'لابتوب ديل XPS 13', TRUE, 1),
(4, '/uploads/products/macbook_air_1.jpg', 'ماك بوك آير M2', TRUE, 1),
(5, '/uploads/products/canon_r6_1.jpg', 'كاميرا كانون EOS R6', TRUE, 1);

-- إدراج خصائص المنتجات
INSERT INTO product_attributes (product_id, attribute_name, attribute_value, sort_order) VALUES
(1, 'الشاشة', '6.7 بوصة Super Retina XDR', 1),
(1, 'المعالج', 'A17 Pro', 2),
(1, 'الذاكرة', '256 جيجابايت', 3),
(1, 'الكاميرا', '48 ميجابكسل ثلاثية', 4),
(1, 'البطارية', 'حتى 29 ساعة تشغيل فيديو', 5),
(2, 'الشاشة', '6.8 بوصة Dynamic AMOLED', 1),
(2, 'المعالج', 'Snapdragon 8 Gen 3', 2),
(2, 'الذاكرة', '512 جيجابايت', 3),
(2, 'الكاميرا', '200 ميجابكسل رباعية', 4),
(3, 'المعالج', 'Intel Core i7-1360P', 1),
(3, 'الذاكرة', '16 جيجا DDR5', 2),
(3, 'التخزين', '512 جيجا SSD', 3),
(3, 'الشاشة', '13.4 بوصة 4K', 4),
(4, 'المعالج', 'Apple M2', 1),
(4, 'الذاكرة', '8 جيجا موحدة', 2),
(4, 'التخزين', '256 جيجا SSD', 3),
(4, 'الشاشة', '13.6 بوصة Liquid Retina', 4);

-- إدراج طلبات تجريبية
INSERT INTO orders (
    order_number,
    customer_id,
    seller_id,
    customer_name,
    customer_email,
    customer_phone,
    customer_whatsapp,
    shipping_address,
    shipping_city,
    subtotal,
    tax_amount,
    shipping_amount,
    total_amount,
    commission_rate,
    commission_amount,
    admin_commission,
    seller_profit,
    status,
    payment_method,
    payment_status,
    customer_notes,
    created_at
) VALUES
('ORD-2024-001', 3, 2, 'فاطمة الزهراء العميلة', '<EMAIL>', '+212 6 11 22 33 44', '+212 6 11 22 33 44', 'شارع محمد الخامس، حي النهضة', 'الدار البيضاء', 14999.00, 0.00, 50.00, 15049.00, 5.00, 749.95, 749.95, 14299.05, 'delivered', 'cash_on_delivery', 'paid', 'يرجى التوصيل في المساء', DATE_SUB(NOW(), INTERVAL 5 DAY)),
('ORD-2024-002', 3, 2, 'فاطمة الزهراء العميلة', '<EMAIL>', '+212 6 11 22 33 44', '+212 6 11 22 33 44', 'شارع محمد الخامس، حي النهضة', 'الدار البيضاء', 12999.00, 0.00, 50.00, 13049.00, 5.00, 649.95, 649.95, 12399.05, 'shipped', 'cash_on_delivery', 'pending', NULL, DATE_SUB(NOW(), INTERVAL 2 DAY)),
('ORD-2024-003', NULL, 2, 'محمد الأمين', '<EMAIL>', '+212 6 55 44 33 22', '+212 6 55 44 33 22', 'شارع الحسن الثاني، أكدال', 'الرباط', 17999.00, 0.00, 0.00, 17999.00, 5.00, 899.95, 899.95, 17099.05, 'processing', 'bank_transfer', 'paid', 'طلب عاجل', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('ORD-2024-004', 3, 2, 'فاطمة الزهراء العميلة', '<EMAIL>', '+212 6 11 22 33 44', '+212 6 11 22 33 44', 'شارع محمد الخامس، حي النهضة', 'الدار البيضاء', 16999.00, 0.00, 50.00, 17049.00, 5.00, 849.95, 849.95, 16199.05, 'pending', 'cash_on_delivery', 'pending', NULL, NOW());

-- إدراج عناصر الطلبات
INSERT INTO order_items (order_id, product_id, product_name, product_sku, quantity, price, total) VALUES
(1, 1, 'آيفون 15 برو ماكس', 'IPHONE15PM256', 1, 14999.00, 14999.00),
(2, 2, 'سامسونج جالاكسي S24 الترا', 'GALAXY-S24-ULTRA', 1, 12999.00, 12999.00),
(3, 3, 'لابتوب ديل XPS 13', 'DELL-XPS13-I7', 1, 17999.00, 17999.00),
(4, 4, 'ماك بوك آير M2', 'MACBOOK-AIR-M2', 1, 16999.00, 16999.00);

-- إدراج تاريخ حالة الطلبات
INSERT INTO order_status_history (order_id, status, notes, changed_by, created_at) VALUES
(1, 'pending', 'تم إنشاء الطلب', 3, DATE_SUB(NOW(), INTERVAL 5 DAY)),
(1, 'confirmed', 'تم تأكيد الطلب من البائع', 2, DATE_SUB(NOW(), INTERVAL 4 DAY)),
(1, 'processing', 'جاري تجهيز الطلب', 2, DATE_SUB(NOW(), INTERVAL 3 DAY)),
(1, 'shipped', 'تم شحن الطلب', 2, DATE_SUB(NOW(), INTERVAL 2 DAY)),
(1, 'delivered', 'تم تسليم الطلب بنجاح', 1, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(2, 'pending', 'تم إنشاء الطلب', 3, DATE_SUB(NOW(), INTERVAL 2 DAY)),
(2, 'confirmed', 'تم تأكيد الطلب', 2, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(2, 'shipped', 'تم شحن الطلب', 2, NOW()),
(3, 'pending', 'تم إنشاء الطلب', NULL, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(3, 'processing', 'جاري تجهيز الطلب', 2, NOW()),
(4, 'pending', 'تم إنشاء الطلب', 3, NOW());

-- إدراج تقييمات تجريبية
INSERT INTO reviews (product_id, customer_id, order_id, rating, title, comment, status, is_verified_purchase, created_at) VALUES
(1, 3, 1, 5, 'منتج ممتاز', 'هاتف رائع بمواصفات عالية، أنصح بشرائه', 'approved', TRUE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(1, NULL, NULL, 4, 'جودة عالية', 'المنتج جيد لكن السعر مرتفع قليلاً', 'approved', FALSE, DATE_SUB(NOW(), INTERVAL 3 DAY));

-- إدراج كوبون خصم تجريبي
INSERT INTO coupons (code, description, discount_type, discount_value, minimum_amount, usage_limit, start_date, end_date, status) VALUES
('WELCOME10', 'خصم 10% للعملاء الجدد', 'percentage', 10.00, 500.00, 100, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 'active'),
('SAVE50', 'خصم 50 درهم على الطلبات', 'fixed_amount', 50.00, 200.00, 50, NOW(), DATE_ADD(NOW(), INTERVAL 15 DAY), 'active');

-- ===================================================================
-- إنشاء مستخدم قاعدة البيانات (اختياري)
-- ===================================================================
-- CREATE USER 'proget_user'@'localhost' IDENTIFIED BY 'ProGet@2024!';
-- GRANT ALL PRIVILEGES ON proget_db.* TO 'proget_user'@'localhost';
-- FLUSH PRIVILEGES;

-- ===================================================================
-- ملاحظات مهمة
-- ===================================================================
/*
معلومات تسجيل الدخول:

1. المدير:
   - البريد الإلكتروني: <EMAIL>
   - كلمة المرور: password
   - الدور: admin

2. البائع التجريبي:
   - البريد الإلكتروني: <EMAIL>
   - كلمة المرور: password
   - الدور: seller

3. العميل التجريبي:
   - البريد الإلكتروني: <EMAIL>
   - كلمة المرور: password
   - الدور: customer

ملاحظات:
- جميع كلمات المرور مشفرة باستخدام bcrypt
- تم إنشاء بيانات تجريبية للاختبار
- يمكن تغيير كلمات المرور من لوحة التحكم
- تأكد من تحديث إعدادات قاعدة البيانات في ملف config.php
*/

-- ===================================================================
-- جدول الإشعارات
-- ===================================================================
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    type ENUM('order', 'product', 'system', 'promotion', 'review') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,

    -- بيانات إضافية
    data JSON,
    related_id INT, -- معرف العنصر المرتبط (طلب، منتج، إلخ)

    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- ===================================================================
-- جدول الصفحات الثابتة
-- ===================================================================
CREATE TABLE pages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content LONGTEXT,
    excerpt TEXT,

    -- SEO
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,

    status ENUM('published', 'draft', 'private') DEFAULT 'draft',
    template VARCHAR(100) DEFAULT 'default',

    -- ترتيب وتنظيم
    parent_id INT,
    sort_order INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (parent_id) REFERENCES pages(id) ON DELETE SET NULL,

    INDEX idx_slug (slug),
    INDEX idx_status (status),
    INDEX idx_parent_id (parent_id)
);

-- ===================================================================
-- جدول إعدادات النظام
-- ===================================================================
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value LONGTEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json', 'text') DEFAULT 'string',
    description TEXT,
    group_name VARCHAR(50) DEFAULT 'general',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_setting_key (setting_key),
    INDEX idx_group_name (group_name)
);

-- ===================================================================
-- جدول سجل النشاطات
-- ===================================================================
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    description TEXT,

    -- تفاصيل النشاط
    model_type VARCHAR(100), -- نوع النموذج (order, product, user, etc.)
    model_id INT, -- معرف النموذج

    -- بيانات إضافية
    properties JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,

    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_model_type (model_type),
    INDEX idx_model_id (model_id),
    INDEX idx_created_at (created_at)
);

-- ===================================================================
-- جدول جلسات المستخدمين
-- ===================================================================
CREATE TABLE user_sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    payload LONGTEXT NOT NULL,
    last_activity INT NOT NULL,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_id (user_id),
    INDEX idx_last_activity (last_activity)
);

-- ===================================================================
-- جدول رموز إعادة تعيين كلمة المرور
-- ===================================================================
CREATE TABLE password_resets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_email (email),
    INDEX idx_token (token),
    INDEX idx_expires_at (expires_at)
);

-- ===================================================================
-- جدول رموز التحقق من البريد الإلكتروني
-- ===================================================================
CREATE TABLE email_verifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    email VARCHAR(100) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    verified_at TIMESTAMP NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,

    INDEX idx_user_id (user_id),
    INDEX idx_email (email),
    INDEX idx_token (token)
);

-- ===================================================================
-- جدول الشحن والتوصيل
-- ===================================================================
CREATE TABLE shipping_zones (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,

    -- المناطق المشمولة
    cities JSON, -- قائمة المدن
    postal_codes JSON, -- الرموز البريدية

    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_name (name),
    INDEX idx_is_active (is_active)
);

-- ===================================================================
-- جدول طرق الشحن
-- ===================================================================
CREATE TABLE shipping_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    zone_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,

    -- تكلفة الشحن
    cost_type ENUM('fixed', 'percentage', 'weight_based', 'free') NOT NULL,
    cost_value DECIMAL(10,2) DEFAULT 0.00,

    -- شروط الشحن المجاني
    free_shipping_min_amount DECIMAL(10,2),

    -- أوقات التسليم
    delivery_time_min INT, -- بالأيام
    delivery_time_max INT, -- بالأيام

    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (zone_id) REFERENCES shipping_zones(id) ON DELETE CASCADE,

    INDEX idx_zone_id (zone_id),
    INDEX idx_is_active (is_active)
);
