<?php
/**
 * ملف الدوال الأساسية - نسخة نظيفة ومبسطة
 * بدون تعقيدات أو حماية معقدة
 */

// تضمين قاعدة البيانات
require_once __DIR__ . '/../config/database.php';

// دوال تنظيف البيانات البسيطة
function cleanInput($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

function cleanEmail($email) {
    return filter_var(trim($email), FILTER_SANITIZE_EMAIL);
}

// دوال التحقق البسيطة
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function isValidPassword($password) {
    return strlen($password) >= 6;
}

// دوال كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function checkPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دوال الجلسة البسيطة
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function isSeller() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'seller';
}

// دالة تسجيل الخروج البسيطة
function logout() {
    $_SESSION = array();
    session_destroy();
}

// دالة إعادة التوجيه
function goTo($url) {
    header("Location: " . $url);
    exit();
}

// دوال عرض الرسائل
function successMessage($message) {
    return '<div class="alert alert-success">' . cleanInput($message) . '</div>';
}

function errorMessage($message) {
    return '<div class="alert alert-danger">' . cleanInput($message) . '</div>';
}

function warningMessage($message) {
    return '<div class="alert alert-warning">' . cleanInput($message) . '</div>';
}

// دوال التنسيق
function formatPrice($price) {
    return number_format($price, 2) . ' د.م';
}

function formatDate($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// دالة الحصول على المستخدم الحالي
function getCurrentUser() {
    global $db;
    
    if (!isLoggedIn()) {
        return false;
    }
    
    try {
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return false;
    }
}

// دالة فحص الصلاحيات
function requireLogin() {
    if (!isLoggedIn()) {
        goTo('../pages/login.php');
    }
}

function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        goTo('../pages/login.php');
    }
}

function requireSeller() {
    requireLogin();
    if (!isSeller()) {
        goTo('../pages/login.php');
    }
}

// دوال قاعدة البيانات البسيطة
function dbQuery($sql, $params = []) {
    global $db;
    try {
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (Exception $e) {
        return false;
    }
}

function dbFetch($sql, $params = []) {
    $stmt = dbQuery($sql, $params);
    return $stmt ? $stmt->fetch(PDO::FETCH_ASSOC) : false;
}

function dbFetchAll($sql, $params = []) {
    $stmt = dbQuery($sql, $params);
    return $stmt ? $stmt->fetchAll(PDO::FETCH_ASSOC) : [];
}

function dbInsert($table, $data) {
    global $db;
    try {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        $sql = "INSERT INTO $table ($columns) VALUES ($placeholders)";
        $stmt = $db->prepare($sql);
        return $stmt->execute($data);
    } catch (Exception $e) {
        return false;
    }
}

function dbUpdate($table, $data, $where, $whereParams = []) {
    global $db;
    try {
        $set = [];
        foreach ($data as $key => $value) {
            $set[] = "$key = :$key";
        }
        $setClause = implode(', ', $set);
        $sql = "UPDATE $table SET $setClause WHERE $where";
        $stmt = $db->prepare($sql);
        return $stmt->execute(array_merge($data, $whereParams));
    } catch (Exception $e) {
        return false;
    }
}

// دالة إنشاء رقم طلب
function generateOrderNumber() {
    return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

// دالة رفع الصور البسيطة
function uploadImage($file, $folder = 'uploads') {
    if (!isset($file) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }
    
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        return false;
    }
    
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = uniqid() . '.' . $extension;
    $uploadDir = "assets/images/$folder/";
    
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $uploadDir . $filename)) {
        return $filename;
    }
    
    return false;
}

// دالة بسيطة لحماية CSRF
function getToken() {
    if (!isset($_SESSION['token'])) {
        $_SESSION['token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['token'];
}

function checkToken($token) {
    return isset($_SESSION['token']) && $_SESSION['token'] === $token;
}

function tokenField() {
    return '<input type="hidden" name="token" value="' . getToken() . '">';
}
?>
