<?php
/**
 * صفحة تسجيل الدخول - نسخة مبسطة ونظيفة
 */

// بدء الجلسة
session_start();

// تضمين الدوال
require_once '../includes/functions_clean.php';

$errors = [];
$success = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = cleanEmail($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    // التحقق من البيانات
    if (empty($email)) {
        $errors[] = 'يرجى إدخال البريد الإلكتروني';
    } elseif (!isValidEmail($email)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    if (empty($password)) {
        $errors[] = 'يرجى إدخال كلمة المرور';
    }
    
    // محاولة تسجيل الدخول
    if (empty($errors)) {
        $user = dbFetch("SELECT * FROM users WHERE email = ?", [$email]);
        
        if ($user && checkPassword($password, $user['password'])) {
            if ($user['status'] === 'active') {
                // تسجيل دخول ناجح
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['full_name'];
                $_SESSION['user_role'] = $user['role'];
                
                // إعادة التوجيه حسب الدور
                if ($user['role'] === 'admin') {
                    goTo('../admin/index.php');
                } else {
                    goTo('../pages/dashboard.php');
                }
            } else {
                $errors[] = 'حسابك غير مفعل. يرجى انتظار موافقة الإدارة.';
            }
        } else {
            $errors[] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - ProGet</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .login-body {
            padding: 40px;
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 15px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }
    </style>
</head>
<body>

<a href="../index.php" class="back-link">
    <i class="fas fa-arrow-right me-2"></i>العودة للرئيسية
</a>

<div class="login-container">
    <div class="login-header">
        <h2><i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول</h2>
        <p class="mb-0 mt-2">أدخل بياناتك للوصول إلى حسابك</p>
    </div>

    <div class="login-body">
        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo cleanInput($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <?php echo cleanInput($success); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <?php echo tokenField(); ?>

            <div class="mb-3">
                <label for="email" class="form-label">البريد الإلكتروني *</label>
                <input type="email" class="form-control" id="email" name="email"
                       value="<?php echo cleanInput($email ?? ''); ?>"
                       required>
            </div>

            <div class="mb-4">
                <label for="password" class="form-label">كلمة المرور *</label>
                <input type="password" class="form-control" id="password" name="password"
                       required>
            </div>

            <div class="d-grid gap-2 mb-4">
                <button type="submit" class="btn btn-login btn-lg text-white">
                    <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                </button>
            </div>
        </form>

        <div class="text-center">
            <p class="text-muted">ليس لديك حساب؟ <a href="register_clean.php" class="text-decoration-none fw-bold">إنشاء حساب جديد</a></p>
        </div>
        
        <div class="text-center mt-3">
            <small class="text-muted">
                <strong>للاختبار:</strong><br>
                البريد: <EMAIL><br>
                كلمة المرور: password
            </small>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
