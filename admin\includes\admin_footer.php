    </div>
</main>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Admin Dashboard JS -->
<script src="assets/js/admin-dashboard.js"></script>

<script>
// تخصيصات إضافية لصفحات المدير
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الوقت في الشريط العلوي
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }
    
    // تحديث الوقت كل ثانية
    updateTime();
    setInterval(updateTime, 1000);
    
    // إدارة قائمة المستخدم
    initUserDropdown();
    
    // تحديث عدادات الإشعارات
    updateNotificationCounts();
    
    // تحديث الإشعارات كل 30 ثانية
    setInterval(updateNotificationCounts, 30000);
});

// دوال إدارة قائمة المستخدم
function initUserDropdown() {
    const userProfileToggle = document.getElementById('userProfileToggle');
    const userDropdown = document.getElementById('userDropdown');
    
    if (userProfileToggle && userDropdown) {
        userProfileToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            userDropdown.classList.toggle('show');
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!userProfileToggle.contains(e.target) && !userDropdown.contains(e.target)) {
                userDropdown.classList.remove('show');
            }
        });
        
        // إغلاق القائمة عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                userDropdown.classList.remove('show');
            }
        });
    }
}

// تحديث عدادات الإشعارات
function updateNotificationCounts() {
    // يمكن استبدال هذا بـ AJAX call لجلب البيانات الحقيقية
    
    // محاكاة تحديث العدادات
    const ordersBadge = document.getElementById('ordersBadge');
    const usersBadge = document.getElementById('usersBadge');
    
    // إضافة تأثير تحديث
    [ordersBadge, usersBadge].forEach(badge => {
        if (badge && !badge.classList.contains('zero')) {
            badge.style.animation = 'pulse 1s ease-in-out';
            setTimeout(() => {
                badge.style.animation = '';
            }, 1000);
        }
    });
}

// دالة لإضافة إشعار جديد (للاستخدام المستقبلي)
function addNewNotification(type, count) {
    const badge = document.getElementById(type + 'Badge');
    if (badge) {
        badge.textContent = count;
        badge.classList.toggle('zero', count === 0);
        
        if (count > 0) {
            badge.style.animation = 'pulse 1s ease-in-out';
            setTimeout(() => {
                badge.style.animation = '';
            }, 1000);
        }
    }
}

// JavaScript إضافي خاص بالصفحة
<?php if (isset($additional_js)): ?>
<?php echo $additional_js; ?>
<?php endif; ?>
</script>

<!-- CSS إضافي للصفحة -->
<?php if (isset($additional_inline_css)): ?>
<style>
<?php echo $additional_inline_css; ?>
</style>
<?php endif; ?>

</body>
</html>
