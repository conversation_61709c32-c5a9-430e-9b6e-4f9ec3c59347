<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات البائع
requireSeller();

$pageTitle = 'الملف الشخصي';
$currentUser = getCurrentUser();
$message = '';
$messageType = '';

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_profile') {
        $full_name = sanitizeInput($_POST['full_name'] ?? '');
        $email = sanitizeInput($_POST['email'] ?? '');
        $phone = sanitizeInput($_POST['phone'] ?? '');
        $address = sanitizeInput($_POST['address'] ?? '');
        $city = sanitizeInput($_POST['city'] ?? '');
        $country = sanitizeInput($_POST['country'] ?? '');
        $business_name = sanitizeInput($_POST['business_name'] ?? '');
        $business_type = sanitizeInput($_POST['business_type'] ?? '');
        $tax_number = sanitizeInput($_POST['tax_number'] ?? '');
        $bank_account = sanitizeInput($_POST['bank_account'] ?? '');
        
        $errors = [];
        
        // التحقق من صحة البيانات
        if (empty($full_name)) {
            $errors[] = 'الاسم الكامل مطلوب';
        }
        
        if (empty($email)) {
            $errors[] = 'البريد الإلكتروني مطلوب';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        // التحقق من عدم تكرار البريد الإلكتروني
        if (empty($errors)) {
            try {
                $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $currentUser['id']]);
                if ($stmt->fetch()) {
                    $errors[] = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
                }
            } catch (PDOException $e) {
                $errors[] = 'حدث خطأ في التحقق من البيانات';
            }
        }
        
        // تحديث البيانات
        if (empty($errors)) {
            try {
                $stmt = $db->prepare("
                    UPDATE users SET 
                        full_name = ?, email = ?, phone = ?, address = ?, city = ?, 
                        country = ?, business_name = ?, business_type = ?, tax_number = ?, 
                        bank_account = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                
                $stmt->execute([
                    $full_name, $email, $phone, $address, $city, 
                    $country, $business_name, $business_type, $tax_number, 
                    $bank_account, $currentUser['id']
                ]);
                
                // تحديث بيانات الجلسة
                $_SESSION['full_name'] = $full_name;
                
                $message = 'تم تحديث الملف الشخصي بنجاح';
                $messageType = 'success';
                
                // إعادة جلب بيانات المستخدم المحدثة
                $currentUser = getCurrentUser();
                
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تحديث البيانات';
                $messageType = 'error';
            }
        } else {
            $message = implode('<br>', $errors);
            $messageType = 'error';
        }
    }
    
    elseif ($action === 'change_password') {
        $current_password = $_POST['current_password'] ?? '';
        $new_password = $_POST['new_password'] ?? '';
        $confirm_password = $_POST['confirm_password'] ?? '';
        
        $errors = [];
        
        // التحقق من صحة البيانات
        if (empty($current_password)) {
            $errors[] = 'كلمة المرور الحالية مطلوبة';
        }
        
        if (empty($new_password)) {
            $errors[] = 'كلمة المرور الجديدة مطلوبة';
        } elseif (strlen($new_password) < 6) {
            $errors[] = 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل';
        }
        
        if ($new_password !== $confirm_password) {
            $errors[] = 'تأكيد كلمة المرور غير متطابق';
        }
        
        // التحقق من كلمة المرور الحالية
        if (empty($errors)) {
            if (!verifyPassword($current_password, $currentUser['password'])) {
                $errors[] = 'كلمة المرور الحالية غير صحيحة';
            }
        }
        
        // تحديث كلمة المرور
        if (empty($errors)) {
            try {
                $hashedPassword = hashPassword($new_password);
                $stmt = $db->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$hashedPassword, $currentUser['id']]);
                
                $message = 'تم تغيير كلمة المرور بنجاح';
                $messageType = 'success';
                
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تغيير كلمة المرور';
                $messageType = 'error';
            }
        } else {
            $message = implode('<br>', $errors);
            $messageType = 'error';
        }
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <h5 class="text-primary">مرحباً <?php echo htmlspecialchars($currentUser['full_name']); ?></h5>
                    <small class="text-muted">بائع</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-bag me-2"></i>المنتجات المتاحة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my-products.php">
                            <i class="fas fa-boxes me-2"></i>منتجاتي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="profile.php">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link text-danger" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">الملف الشخصي</h1>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- معلومات الحساب -->
                <div class="col-md-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">معلومات الحساب</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="update_profile">
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل *</label>
                                        <input type="text" class="form-control" name="full_name" value="<?php echo htmlspecialchars($currentUser['full_name']); ?>" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني *</label>
                                        <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($currentUser['email']); ?>" required>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" name="phone" value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المدينة</label>
                                        <input type="text" class="form-control" name="city" value="<?php echo htmlspecialchars($currentUser['city'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الدولة</label>
                                        <input type="text" class="form-control" name="country" value="<?php echo htmlspecialchars($currentUser['country'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم النشاط التجاري</label>
                                        <input type="text" class="form-control" name="business_name" value="<?php echo htmlspecialchars($currentUser['business_name'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع النشاط التجاري</label>
                                        <input type="text" class="form-control" name="business_type" value="<?php echo htmlspecialchars($currentUser['business_type'] ?? ''); ?>">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الرقم الضريبي</label>
                                        <input type="text" class="form-control" name="tax_number" value="<?php echo htmlspecialchars($currentUser['tax_number'] ?? ''); ?>">
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">العنوان</label>
                                    <textarea class="form-control" name="address" rows="3"><?php echo htmlspecialchars($currentUser['address'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">رقم الحساب البنكي</label>
                                    <input type="text" class="form-control" name="bank_account" value="<?php echo htmlspecialchars($currentUser['bank_account'] ?? ''); ?>">
                                </div>
                                
                                <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- معلومات إضافية -->
                <div class="col-md-4">
                    <!-- إحصائيات الحساب -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="mb-0">إحصائيات الحساب</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <small class="text-muted">اسم المستخدم</small>
                                <div><strong><?php echo htmlspecialchars($currentUser['username']); ?></strong></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">حالة الحساب</small>
                                <div>
                                    <?php
                                    $statusLabels = [
                                        'pending' => ['معلق', 'warning'],
                                        'active' => ['نشط', 'success'],
                                        'suspended' => ['معلق', 'danger']
                                    ];
                                    $status = $statusLabels[$currentUser['status']] ?? ['غير معروف', 'secondary'];
                                    ?>
                                    <span class="badge bg-<?php echo $status[1]; ?>"><?php echo $status[0]; ?></span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">معدل العمولة</small>
                                <div><strong><?php echo number_format($currentUser['commission_rate'], 2); ?>%</strong></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">إجمالي المبيعات</small>
                                <div><strong><?php echo number_format($currentUser['total_sales'], 2); ?> ر.س</strong></div>
                            </div>
                            <div class="mb-3">
                                <small class="text-muted">إجمالي العمولة</small>
                                <div><strong><?php echo number_format($currentUser['total_commission'], 2); ?> ر.س</strong></div>
                            </div>
                            <div>
                                <small class="text-muted">تاريخ التسجيل</small>
                                <div><?php echo date('Y-m-d', strtotime($currentUser['created_at'])); ?></div>
                            </div>
                        </div>
                    </div>

                    <!-- تغيير كلمة المرور -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">تغيير كلمة المرور</h6>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="change_password">
                                
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" class="form-control" name="current_password" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" name="new_password" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" name="confirm_password" required>
                                </div>
                                
                                <button type="submit" class="btn btn-warning btn-sm">تغيير كلمة المرور</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
