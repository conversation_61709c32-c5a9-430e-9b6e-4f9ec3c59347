<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات البائع
requireSeller();

$pageTitle = 'منتجاتي';
$currentUser = getCurrentUser();
$message = '';
$messageType = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_price') {
        $sellerProductId = (int)($_POST['seller_product_id'] ?? 0);
        $newPrice = (float)($_POST['new_price'] ?? 0);
        
        if ($sellerProductId > 0 && $newPrice > 0) {
            try {
                $stmt = $db->prepare("UPDATE seller_products SET selling_price = ? WHERE id = ? AND seller_id = ?");
                $stmt->execute([$newPrice, $sellerProductId, $currentUser['id']]);
                $message = 'تم تحديث السعر بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تحديث السعر';
                $messageType = 'error';
            }
        }
    } elseif ($action === 'toggle_status') {
        $sellerProductId = (int)($_POST['seller_product_id'] ?? 0);
        $newStatus = $_POST['new_status'] ?? '';
        
        if ($sellerProductId > 0 && in_array($newStatus, ['active', 'inactive'])) {
            try {
                $stmt = $db->prepare("UPDATE seller_products SET status = ? WHERE id = ? AND seller_id = ?");
                $stmt->execute([$newStatus, $sellerProductId, $currentUser['id']]);
                $message = 'تم تحديث حالة المنتج بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تحديث حالة المنتج';
                $messageType = 'error';
            }
        }
    } elseif ($action === 'remove') {
        $sellerProductId = (int)($_POST['seller_product_id'] ?? 0);
        
        if ($sellerProductId > 0) {
            try {
                $stmt = $db->prepare("DELETE FROM seller_products WHERE id = ? AND seller_id = ?");
                $stmt->execute([$sellerProductId, $currentUser['id']]);
                $message = 'تم إزالة المنتج من متجرك بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في إزالة المنتج';
                $messageType = 'error';
            }
        }
    }
}

// فلترة المنتجات
$statusFilter = $_GET['status'] ?? '';
$searchQuery = $_GET['search'] ?? '';

$whereConditions = ["sp.seller_id = ?"];
$params = [$currentUser['id']];

if ($statusFilter) {
    $whereConditions[] = "sp.status = ?";
    $params[] = $statusFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(p.name LIKE ? OR p.description LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب منتجات البائع
try {
    $stmt = $db->prepare("
        SELECT sp.*, p.name, p.description, p.category, p.supplier_name, p.supplier_price, 
               p.image_url, p.stock_status, p.status as product_status,
               (sp.selling_price - p.supplier_price) as profit_amount,
               CASE WHEN p.supplier_price > 0 THEN ((sp.selling_price - p.supplier_price) / p.supplier_price) * 100 ELSE 0 END as profit_margin
        FROM seller_products sp 
        JOIN products p ON sp.product_id = p.id 
        WHERE $whereClause 
        ORDER BY sp.added_at DESC
    ");
    $stmt->execute($params);
    $myProducts = $stmt->fetchAll();
} catch (PDOException $e) {
    $myProducts = [];
}

// حساب الإحصائيات
$totalProducts = count($myProducts);
$activeProducts = count(array_filter($myProducts, function($p) { return $p['status'] === 'active'; }));
$totalPotentialProfit = array_sum(array_column($myProducts, 'profit_amount'));

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="https://via.placeholder.com/80x80?text=<?php echo substr($currentUser['full_name'], 0, 1); ?>" 
                         class="rounded-circle mb-2" alt="Seller">
                    <h6 class="text-white"><?php echo htmlspecialchars($currentUser['full_name']); ?></h6>
                    <small class="text-white-50">بائع</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-bag me-2"></i>المنتجات المتاحة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="my-products.php">
                            <i class="fas fa-boxes me-2"></i>منتجاتي
                            <span class="badge bg-info"><?php echo $totalProducts; ?></span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">منتجاتي</h1>
                <a href="products.php" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة منتجات جديدة
                </a>
            </div>

            <?php if ($message): ?>
                <?php echo showMessage($message, $messageType); ?>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3><?php echo $totalProducts; ?></h3>
                                    <p class="mb-0">إجمالي المنتجات</p>
                                </div>
                                <i class="fas fa-boxes fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3><?php echo $activeProducts; ?></h3>
                                    <p class="mb-0">المنتجات النشطة</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h3><?php echo formatPrice($totalPotentialProfit); ?></h3>
                                    <p class="mb-0">الربح المحتمل</p>
                                </div>
                                <i class="fas fa-chart-line fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="البحث بالاسم أو الوصف">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <a href="my-products.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="row">
                <?php if (!empty($myProducts)): ?>
                    <?php foreach ($myProducts as $product): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card product-card h-100">
                                <div class="position-relative">
                                    <img src="<?php echo htmlspecialchars($product['image_url']); ?>" 
                                         class="card-img-top product-image" 
                                         alt="<?php echo htmlspecialchars($product['name']); ?>">
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-<?php echo $product['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo $product['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                    <p class="card-text text-muted">
                                        <?php echo htmlspecialchars(substr($product['description'], 0, 80)) . '...'; ?>
                                    </p>
                                    
                                    <div class="mb-2">
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($product['category']); ?></span>
                                        <span class="badge bg-info"><?php echo getStockStatusText($product['stock_status']); ?></span>
                                    </div>
                                    
                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            <small class="text-muted">سعر المورد</small><br>
                                            <span class="text-primary"><?php echo formatPrice($product['supplier_price']); ?></span>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">سعر البيع</small><br>
                                            <span class="text-success fw-bold"><?php echo formatPrice($product['selling_price']); ?></span>
                                        </div>
                                        <div class="col-4">
                                            <small class="text-muted">الربح</small><br>
                                            <span class="text-warning"><?php echo formatPrice($product['profit_amount']); ?></span>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center mb-3">
                                        <small class="text-muted">
                                            هامش الربح: <strong><?php echo number_format($product['profit_margin'], 1); ?>%</strong>
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="card-footer">
                                    <div class="row g-2">
                                        <div class="col-6">
                                            <button type="button" class="btn btn-sm btn-outline-primary w-100" 
                                                    onclick="editPrice(<?php echo $product['id']; ?>, <?php echo $product['selling_price']; ?>)">
                                                <i class="fas fa-edit me-1"></i>تعديل السعر
                                            </button>
                                        </div>
                                        <div class="col-6">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="seller_product_id" value="<?php echo $product['id']; ?>">
                                                <input type="hidden" name="new_status" value="<?php echo $product['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                                <button type="submit" class="btn btn-sm btn-<?php echo $product['status'] === 'active' ? 'warning' : 'success'; ?> w-100">
                                                    <i class="fas fa-<?php echo $product['status'] === 'active' ? 'pause' : 'play'; ?> me-1"></i>
                                                    <?php echo $product['status'] === 'active' ? 'إيقاف' : 'تفعيل'; ?>
                                                </button>
                                            </form>
                                        </div>
                                        <div class="col-12">
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="remove">
                                                <input type="hidden" name="seller_product_id" value="<?php echo $product['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-outline-danger w-100" 
                                                        onclick="return confirm('هل أنت متأكد من إزالة هذا المنتج من متجرك؟')">
                                                    <i class="fas fa-trash me-1"></i>إزالة من متجري
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات في متجرك</h5>
                            <p class="text-muted">ابدأ بإضافة منتجات من المنتجات المتاحة</p>
                            <a href="products.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>تصفح المنتجات المتاحة
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<!-- Edit Price Modal -->
<div class="modal fade" id="editPriceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل سعر البيع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editPriceForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_price">
                    <input type="hidden" name="seller_product_id" id="editProductId">
                    
                    <div class="mb-3">
                        <label for="new_price" class="form-label">السعر الجديد</label>
                        <input type="number" class="form-control" id="new_price" name="new_price" 
                               step="0.01" min="0" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editPrice(productId, currentPrice) {
    document.getElementById('editProductId').value = productId;
    document.getElementById('new_price').value = currentPrice;
    
    const modal = new bootstrap.Modal(document.getElementById('editPriceModal'));
    modal.show();
}
</script>

<?php include '../includes/footer.php'; ?>
