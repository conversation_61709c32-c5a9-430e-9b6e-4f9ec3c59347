<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
requireAdmin();

$invoiceId = (int)($_GET['id'] ?? 0);

if ($invoiceId <= 0) {
    die('معرف الفاتورة غير صحيح');
}

// جلب تفاصيل الفاتورة
try {
    $stmt = $db->prepare("
        SELECT i.*, u.full_name as seller_name, u.username as seller_username,
               u.email as seller_email, u.phone as seller_phone, u.address as seller_address,
               u.business_name, u.tax_number, u.bank_account,
               admin.full_name as created_by_name
        FROM invoices i 
        JOIN users u ON i.seller_id = u.id 
        JOIN users admin ON i.created_by = admin.id
        WHERE i.id = ?
    ");
    $stmt->execute([$invoiceId]);
    $invoice = $stmt->fetch();
    
    if (!$invoice) {
        die('الفاتورة غير موجودة');
    }
} catch (PDOException $e) {
    die('حدث خطأ في جلب بيانات الفاتورة');
}

$pageTitle = 'تفاصيل الفاتورة - ' . $invoice['invoice_number'];
include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">تفاصيل الفاتورة</h4>
                    <div>
                        <button onclick="window.print()" class="btn btn-primary btn-sm">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                        <button onclick="window.close()" class="btn btn-secondary btn-sm">
                            <i class="fas fa-times me-1"></i>إغلاق
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- رأس الفاتورة -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h3 class="text-primary">منصة الدروب شيبنج</h3>
                            <p class="mb-1">المملكة العربية السعودية</p>
                            <p class="mb-1">البريد الإلكتروني: <EMAIL></p>
                            <p class="mb-0">الهاتف: +966 50 123 4567</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <h2 class="text-primary">فاتورة</h2>
                            <p class="mb-1"><strong>رقم الفاتورة:</strong> <?php echo htmlspecialchars($invoice['invoice_number']); ?></p>
                            <p class="mb-1"><strong>تاريخ الإصدار:</strong> <?php echo formatDate($invoice['issue_date']); ?></p>
                            <?php if ($invoice['due_date']): ?>
                                <p class="mb-0"><strong>تاريخ الاستحقاق:</strong> <?php echo formatDate($invoice['due_date']); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <hr>

                    <!-- معلومات البائع -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="text-secondary">فاتورة إلى:</h5>
                            <div class="border p-3 rounded">
                                <h6 class="mb-2"><?php echo htmlspecialchars($invoice['seller_name']); ?></h6>
                                <?php if ($invoice['business_name']): ?>
                                    <p class="mb-1"><strong>النشاط التجاري:</strong> <?php echo htmlspecialchars($invoice['business_name']); ?></p>
                                <?php endif; ?>
                                <p class="mb-1"><strong>اسم المستخدم:</strong> @<?php echo htmlspecialchars($invoice['seller_username']); ?></p>
                                <p class="mb-1"><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($invoice['seller_email']); ?></p>
                                <?php if ($invoice['seller_phone']): ?>
                                    <p class="mb-1"><strong>الهاتف:</strong> <?php echo htmlspecialchars($invoice['seller_phone']); ?></p>
                                <?php endif; ?>
                                <?php if ($invoice['seller_address']): ?>
                                    <p class="mb-1"><strong>العنوان:</strong> <?php echo htmlspecialchars($invoice['seller_address']); ?></p>
                                <?php endif; ?>
                                <?php if ($invoice['tax_number']): ?>
                                    <p class="mb-1"><strong>الرقم الضريبي:</strong> <?php echo htmlspecialchars($invoice['tax_number']); ?></p>
                                <?php endif; ?>
                                <?php if ($invoice['bank_account']): ?>
                                    <p class="mb-0"><strong>رقم الحساب البنكي:</strong> <?php echo htmlspecialchars($invoice['bank_account']); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-secondary">تفاصيل الفاتورة:</h5>
                            <div class="border p-3 rounded">
                                <div class="row mb-2">
                                    <div class="col-6"><strong>نوع الفاتورة:</strong></div>
                                    <div class="col-6">
                                        <span class="badge bg-<?php 
                                            echo $invoice['type'] === 'commission' ? 'info' : 
                                                ($invoice['type'] === 'payout' ? 'success' : 'secondary'); 
                                        ?>">
                                            <?php echo getInvoiceTypeText($invoice['type']); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6"><strong>الحالة:</strong></div>
                                    <div class="col-6">
                                        <span class="badge bg-<?php 
                                            echo $invoice['status'] === 'paid' ? 'success' : 
                                                ($invoice['status'] === 'pending' ? 'warning' : 'danger'); 
                                        ?>">
                                            <?php echo getInvoiceStatusText($invoice['status']); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6"><strong>أنشأها:</strong></div>
                                    <div class="col-6"><?php echo htmlspecialchars($invoice['created_by_name']); ?></div>
                                </div>
                                <div class="row">
                                    <div class="col-6"><strong>تاريخ الإنشاء:</strong></div>
                                    <div class="col-6"><?php echo formatDate($invoice['created_at']); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تفاصيل المبلغ -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-secondary">تفاصيل المبلغ:</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>الوصف</th>
                                            <th class="text-end">المبلغ</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <?php if ($invoice['description']): ?>
                                                    <?php echo htmlspecialchars($invoice['description']); ?>
                                                <?php else: ?>
                                                    <?php echo getInvoiceTypeText($invoice['type']); ?>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-end">
                                                <strong class="text-primary"><?php echo formatPrice($invoice['amount']); ?></strong>
                                            </td>
                                        </tr>
                                    </tbody>
                                    <tfoot class="table-light">
                                        <tr>
                                            <th>المجموع الإجمالي</th>
                                            <th class="text-end">
                                                <h5 class="text-primary mb-0"><?php echo formatPrice($invoice['amount']); ?></h5>
                                            </th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات إضافية -->
                    <?php if ($invoice['description']): ?>
                        <div class="row mb-4">
                            <div class="col-12">
                                <h5 class="text-secondary">ملاحظات:</h5>
                                <div class="border p-3 rounded bg-light">
                                    <?php echo nl2br(htmlspecialchars($invoice['description'])); ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- تذييل الفاتورة -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="text-center text-muted">
                                <p class="mb-1">شكراً لك على التعامل معنا</p>
                                <p class="mb-0">هذه فاتورة إلكترونية ولا تحتاج إلى توقيع</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .card-header .btn {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>

<?php include '../includes/footer.php'; ?>
