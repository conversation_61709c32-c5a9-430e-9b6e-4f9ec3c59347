<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
requireAdmin();

$pageTitle = 'إدارة البائعين';
$currentUser = getCurrentUser();
$message = '';
$messageType = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $sellerId = (int)($_POST['seller_id'] ?? 0);
    
    if ($action === 'activate' && $sellerId > 0) {
        try {
            $stmt = $db->prepare("UPDATE users SET status = 'active' WHERE id = ? AND role = 'seller'");
            $stmt->execute([$sellerId]);
            $message = 'تم تفعيل حساب البائع بنجاح';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'حدث خطأ في تفعيل الحساب';
            $messageType = 'error';
        }
    } elseif ($action === 'suspend' && $sellerId > 0) {
        try {
            $stmt = $db->prepare("UPDATE users SET status = 'suspended' WHERE id = ? AND role = 'seller'");
            $stmt->execute([$sellerId]);
            $message = 'تم تعليق حساب البائع بنجاح';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'حدث خطأ في تعليق الحساب';
            $messageType = 'error';
        }
    } elseif ($action === 'delete' && $sellerId > 0) {
        try {
            $stmt = $db->prepare("DELETE FROM users WHERE id = ? AND role = 'seller'");
            $stmt->execute([$sellerId]);
            $message = 'تم حذف حساب البائع بنجاح';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'حدث خطأ في حذف الحساب';
            $messageType = 'error';
        }
    }
}

// فلترة البائعين
$statusFilter = $_GET['status'] ?? '';
$searchQuery = $_GET['search'] ?? '';

$whereConditions = ["role = 'seller'"];
$params = [];

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(full_name LIKE ? OR email LIKE ? OR username LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب البائعين
try {
    $stmt = $db->prepare("SELECT * FROM users WHERE $whereClause ORDER BY created_at DESC");
    $stmt->execute($params);
    $sellers = $stmt->fetchAll();
} catch (PDOException $e) {
    $sellers = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="https://via.placeholder.com/80x80?text=Admin" class="rounded-circle mb-2" alt="Admin">
                    <h6 class="text-white"><?php echo htmlspecialchars($currentUser['full_name']); ?></h6>
                    <small class="text-white-50">مدير النظام</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sellers.php">
                            <i class="fas fa-users me-2"></i>إدارة البائعين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-boxes me-2"></i>إدارة المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>إدارة الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>إعدادات الموقع
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link" href="../pages/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة البائعين</h1>
            </div>

            <?php if ($message): ?>
                <?php echo showMessage($message, $messageType); ?>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="البحث بالاسم أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="suspended" <?php echo $statusFilter === 'suspended' ? 'selected' : ''; ?>>معلق</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <a href="sellers.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sellers Table -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users me-2"></i>قائمة البائعين (<?php echo count($sellers); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($sellers)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم الكامل</th>
                                        <th>اسم المستخدم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الهاتف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sellers as $seller): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($seller['full_name']); ?></strong>
                                                <?php if ($seller['business_name']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($seller['business_name']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($seller['username']); ?></td>
                                            <td><?php echo htmlspecialchars($seller['email']); ?></td>
                                            <td><?php echo htmlspecialchars($seller['phone'] ?? 'غير محدد'); ?></td>
                                            <td>
                                                <span class="badge bg-<?php echo $seller['status'] === 'active' ? 'success' : ($seller['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                                    <?php echo getUserStatusText($seller['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($seller['created_at']); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <?php if ($seller['status'] === 'pending'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="activate">
                                                            <input type="hidden" name="seller_id" value="<?php echo $seller['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-success" title="تفعيل">
                                                                <i class="fas fa-check"></i>
                                                            </button>
                                                        </form>
                                                    <?php elseif ($seller['status'] === 'active'): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="suspend">
                                                            <input type="hidden" name="seller_id" value="<?php echo $seller['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-warning" title="تعليق" 
                                                                    onclick="return confirm('هل أنت متأكد من تعليق هذا الحساب؟')">
                                                                <i class="fas fa-pause"></i>
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="activate">
                                                            <input type="hidden" name="seller_id" value="<?php echo $seller['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-success" title="إعادة تفعيل">
                                                                <i class="fas fa-play"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <a href="seller-details.php?id=<?php echo $seller['id']; ?>" 
                                                       class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    
                                                    <form method="POST" style="display: inline;">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="seller_id" value="<?php echo $seller['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-danger" title="حذف" 
                                                                onclick="return confirm('هل أنت متأكد من حذف هذا البائع؟ سيتم حذف جميع بياناته!')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات</h5>
                            <p class="text-muted">لم يتم العثور على بائعين بالمعايير المحددة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
