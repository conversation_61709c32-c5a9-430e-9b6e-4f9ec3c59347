<?php
// بدء الجلسة
session_start();

// تضمين قاعدة البيانات
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    header('Location: ../pages/login.php');
    exit();
}

if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../pages/dashboard.php');
    exit();
}

$pageTitle = 'إدارة البائعين';

// الحصول على البائعين
try {
    $status_filter = $_GET['status'] ?? 'all';
    
    if ($status_filter === 'all') {
        $stmt = $db->prepare("SELECT * FROM users WHERE role = 'seller' ORDER BY created_at DESC");
        $stmt->execute();
    } else {
        $stmt = $db->prepare("SELECT * FROM users WHERE role = 'seller' AND status = ? ORDER BY created_at DESC");
        $stmt->execute([$status_filter]);
    }
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $users = [];
}

// دوال مساعدة
function formatDate($date) {
    return date('d/m/Y H:i', strtotime($date));
}

function getStatusText($status) {
    $statuses = [
        'pending' => 'في الانتظار',
        'active' => 'نشط',
        'suspended' => 'معلق',
        'rejected' => 'مرفوض'
    ];
    return $statuses[$status] ?? $status;
}

function getStatusClass($status) {
    $classes = [
        'pending' => 'warning',
        'active' => 'success',
        'suspended' => 'secondary',
        'rejected' => 'danger'
    ];
    return $classes[$status] ?? 'secondary';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - لوحة تحكم المدير</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            display: block;
            transition: all 0.3s ease;
        }
        
        .sidebar a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .sidebar a.active {
            background: rgba(255,255,255,0.2);
        }
        
        .main-content {
            margin-right: 250px;
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.8rem;
        }
    </style>
</head>
<body>

<!-- الشريط العلوي -->
<nav class="navbar navbar-expand-lg navbar-dark fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-tachometer-alt me-2"></i>لوحة تحكم المدير
        </a>
        
        <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-1"></i><?php echo $_SESSION['user_name']; ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="../pages/logout.php">تسجيل الخروج</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<!-- الشريط الجانبي -->
<div class="sidebar position-fixed" style="top: 56px; right: 0; width: 250px; z-index: 1000;">
    <div class="p-3">
        <h5>القائمة الرئيسية</h5>
    </div>
    <nav>
        <a href="index.php"><i class="fas fa-home me-2"></i>الرئيسية</a>
        <a href="orders.php"><i class="fas fa-shopping-cart me-2"></i>إدارة الطلبات</a>
        <a href="users_simple.php" class="active"><i class="fas fa-users me-2"></i>إدارة البائعين</a>
        <a href="products.php"><i class="fas fa-box me-2"></i>إدارة المنتجات</a>
        <a href="categories.php"><i class="fas fa-tags me-2"></i>إدارة الفئات</a>
        <a href="reports.php"><i class="fas fa-chart-bar me-2"></i>التقارير</a>
        <a href="settings.php"><i class="fas fa-cog me-2"></i>الإعدادات</a>
    </nav>
</div>

<!-- المحتوى الرئيسي -->
<div class="main-content" style="margin-top: 76px;">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-users me-2"></i>إدارة البائعين
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- فلاتر الحالة -->
                        <div class="mb-3">
                            <div class="btn-group" role="group">
                                <a href="users_simple.php" class="btn btn-outline-primary <?php echo ($status_filter === 'all') ? 'active' : ''; ?>">
                                    جميع البائعين
                                </a>
                                <a href="users_simple.php?status=pending" class="btn btn-outline-warning <?php echo ($status_filter === 'pending') ? 'active' : ''; ?>">
                                    في الانتظار
                                </a>
                                <a href="users_simple.php?status=active" class="btn btn-outline-success <?php echo ($status_filter === 'active') ? 'active' : ''; ?>">
                                    نشط
                                </a>
                                <a href="users_simple.php?status=suspended" class="btn btn-outline-secondary <?php echo ($status_filter === 'suspended') ? 'active' : ''; ?>">
                                    معلق
                                </a>
                                <a href="users_simple.php?status=rejected" class="btn btn-outline-danger <?php echo ($status_filter === 'rejected') ? 'active' : ''; ?>">
                                    مرفوض
                                </a>
                            </div>
                        </div>
                        
                        <!-- جدول البائعين -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم الكامل</th>
                                        <th>اسم المتجر</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>رقم الهاتف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($users)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">لا يوجد بائعين</p>
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($users as $user): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($user['full_name'] ?? 'غير محدد'); ?></td>
                                                <td><?php echo htmlspecialchars($user['store_name'] ?? 'غير محدد'); ?></td>
                                                <td><?php echo htmlspecialchars($user['email'] ?? 'غير محدد'); ?></td>
                                                <td><?php echo htmlspecialchars($user['phone'] ?? 'غير محدد'); ?></td>
                                                <td>
                                                    <span class="badge bg-<?php echo getStatusClass($user['status'] ?? 'pending'); ?>">
                                                        <?php echo getStatusText($user['status'] ?? 'pending'); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo formatDate($user['created_at'] ?? date('Y-m-d H:i:s')); ?></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="user-details.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-primary">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-secondary">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
