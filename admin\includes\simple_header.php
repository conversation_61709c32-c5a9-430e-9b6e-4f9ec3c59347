<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle ?? 'لوحة تحكم المدير'; ?> - ProGet</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
            position: fixed;
            top: 56px;
            right: 0;
            width: 250px;
            z-index: 1000;
        }
        
        .sidebar a {
            color: white;
            text-decoration: none;
            padding: 12px 20px;
            display: block;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .sidebar a.active {
            background: rgba(255,255,255,0.2);
            border-right: 4px solid #fff;
        }
        
        .main-content {
            margin-right: 250px;
            padding: 20px;
            margin-top: 76px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .btn {
            border-radius: 8px;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 20px;
        }
        
        .order-status-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            border-right: 4px solid #667eea;
        }
        
        .order-status-card:hover {
            transform: translateY(-5px);
        }
        
        .order-count {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .order-label {
            font-weight: 600;
            margin: 10px 0;
        }
        
        .quick-action-btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #f8f9fa;
            color: #667eea;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .quick-action-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .stats-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }
        
        .stats-summary h4 {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .orders-overview {
            margin: 30px 0;
        }
        
        .dashboard-filters {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .page-title {
            color: #667eea;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .welcome-text {
            color: #6c757d;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>

<!-- الشريط العلوي -->
<nav class="navbar navbar-expand-lg navbar-dark fixed-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="index.php">
            <i class="fas fa-tachometer-alt me-2"></i>لوحة تحكم المدير - ProGet
        </a>
        
        <div class="navbar-nav ms-auto">
            <div class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                    <i class="fas fa-user me-1"></i><?php echo $_SESSION['user_name'] ?? 'المدير'; ?>
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="../pages/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<!-- الشريط الجانبي -->
<div class="sidebar">
    <div class="p-3">
        <h5><i class="fas fa-bars me-2"></i>القائمة الرئيسية</h5>
    </div>
    <nav>
        <a href="index.php" <?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'class="active"' : ''; ?>>
            <i class="fas fa-home me-2"></i>الرئيسية
        </a>
        <a href="orders.php" <?php echo (basename($_SERVER['PHP_SELF']) === 'orders.php') ? 'class="active"' : ''; ?>>
            <i class="fas fa-shopping-cart me-2"></i>إدارة الطلبات
        </a>
        <a href="users_simple.php" <?php echo (basename($_SERVER['PHP_SELF']) === 'users_simple.php') ? 'class="active"' : ''; ?>>
            <i class="fas fa-users me-2"></i>إدارة البائعين
        </a>
        <a href="products.php" <?php echo (basename($_SERVER['PHP_SELF']) === 'products.php') ? 'class="active"' : ''; ?>>
            <i class="fas fa-box me-2"></i>إدارة المنتجات
        </a>
        <a href="categories.php" <?php echo (basename($_SERVER['PHP_SELF']) === 'categories.php') ? 'class="active"' : ''; ?>>
            <i class="fas fa-tags me-2"></i>إدارة الفئات
        </a>
        <a href="reports.php" <?php echo (basename($_SERVER['PHP_SELF']) === 'reports.php') ? 'class="active"' : ''; ?>>
            <i class="fas fa-chart-bar me-2"></i>التقارير
        </a>
        <a href="settings.php" <?php echo (basename($_SERVER['PHP_SELF']) === 'settings.php') ? 'class="active"' : ''; ?>>
            <i class="fas fa-cog me-2"></i>الإعدادات
        </a>
    </nav>
</div>

<!-- المحتوى الرئيسي -->
<div class="main-content">
    <div class="container-fluid">
