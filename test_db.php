<?php
// اختبار الاتصال بقاعدة البيانات
require_once 'config/database.php';

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    // اختبار الاتصال
    echo "<p>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // فحص جدول المستخدمين
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM users");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>عدد المستخدمين في قاعدة البيانات: " . $result['count'] . "</p>";
    
    // فحص بيانات المدير
    $stmt = $db->prepare("SELECT id, email, full_name, role, status FROM users WHERE email = '<EMAIL>'");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "<h3>بيانات المدير:</h3>";
        echo "<ul>";
        echo "<li>ID: " . $admin['id'] . "</li>";
        echo "<li>البريد الإلكتروني: " . $admin['email'] . "</li>";
        echo "<li>الاسم: " . $admin['full_name'] . "</li>";
        echo "<li>الدور: " . $admin['role'] . "</li>";
        echo "<li>الحالة: " . $admin['status'] . "</li>";
        echo "</ul>";
        
        // اختبار كلمة المرور
        $stmt = $db->prepare("SELECT password FROM users WHERE email = '<EMAIL>'");
        $stmt->execute();
        $passwordData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (password_verify('password', $passwordData['password'])) {
            echo "<p>✅ كلمة المرور صحيحة</p>";
        } else {
            echo "<p>❌ كلمة المرور غير صحيحة</p>";
        }
    } else {
        echo "<p>❌ لم يتم العثور على المدير</p>";
    }
    
    // فحص جميع المستخدمين
    echo "<h3>جميع المستخدمين:</h3>";
    $stmt = $db->prepare("SELECT id, email, full_name, role, status FROM users");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>البريد الإلكتروني</th><th>الاسم</th><th>الدور</th><th>الحالة</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['email'] . "</td>";
            echo "<td>" . $user['full_name'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>لا توجد مستخدمين في قاعدة البيانات</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}
?>
