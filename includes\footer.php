    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><i class="fas fa-store me-2"></i>منصة البائعين</h5>
                    <p>منصة متكاملة لإدارة أعمال البيع بنموذج Drop Shipping. ابدأ رحلتك التجارية معنا اليوم!</p>
                    <div class="social-links">
                        <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white me-3"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <h6>روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="index.php" class="text-white-50">الرئيسية</a></li>
                        <li><a href="pages/about.php" class="text-white-50">من نحن</a></li>
                        <li><a href="pages/contact.php" class="text-white-50">اتصل بنا</a></li>
                        <li><a href="pages/faq.php" class="text-white-50">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                
                <div class="col-md-2">
                    <h6>للبائعين</h6>
                    <ul class="list-unstyled">
                        <li><a href="pages/register.php" class="text-white-50">التسجيل</a></li>
                        <li><a href="pages/login.php" class="text-white-50">تسجيل الدخول</a></li>
                        <li><a href="pages/seller-guide.php" class="text-white-50">دليل البائع</a></li>
                        <li><a href="pages/commission.php" class="text-white-50">نظام العمولة</a></li>
                    </ul>
                </div>
                
                <div class="col-md-2">
                    <h6>الدعم</h6>
                    <ul class="list-unstyled">
                        <li><a href="pages/help.php" class="text-white-50">مركز المساعدة</a></li>
                        <li><a href="pages/terms.php" class="text-white-50">الشروط والأحكام</a></li>
                        <li><a href="pages/privacy.php" class="text-white-50">سياسة الخصوصية</a></li>
                        <li><a href="pages/refund.php" class="text-white-50">سياسة الاسترداد</a></li>
                    </ul>
                </div>
                
                <div class="col-md-2">
                    <h6>تواصل معنا</h6>
                    <ul class="list-unstyled text-white-50">
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i>+966 50 123 4567</li>
                        <li><i class="fas fa-map-marker-alt me-2"></i>الرياض، المملكة العربية السعودية</li>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0 text-white-50">
                        &copy; <?php echo date('Y'); ?> منصة البائعين. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0 text-white-50">
                        تم التطوير بواسطة <a href="#" class="text-white">فريق التطوير</a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js for Dashboard -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="<?php echo isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], '/pages/') !== false ? '../' : ''; ?>assets/js/main.js"></script>
    
    <script>
        // تأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }
        
        // إخفاء الرسائل تلقائياً
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // تحديث الوقت الحالي
        function updateCurrentTime() {
            const now = new Date();
            const timeString = now.toLocaleString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            
            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }
        
        // تحديث الوقت كل دقيقة
        setInterval(updateCurrentTime, 60000);
        updateCurrentTime();
        
        // تحسين تجربة المستخدم للنماذج
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للأزرار
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(function(button) {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });
            
            // التحقق من صحة النماذج
            const forms = document.querySelectorAll('.needs-validation');
            forms.forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                });
            });
            
            // تحسين حقول الإدخال
            const inputs = document.querySelectorAll('.form-control');
            inputs.forEach(function(input) {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });
            });
        });
        
        // وظائف AJAX للتفاعل السريع
        function addToMyProducts(productId) {
            fetch('ajax/add-product.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: productId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('تم إضافة المنتج بنجاح!', 'success');
                    location.reload();
                } else {
                    showNotification(data.message || 'حدث خطأ!', 'error');
                }
            })
            .catch(error => {
                showNotification('حدث خطأ في الاتصال!', 'error');
            });
        }
        
        // عرض الإشعارات
        function showNotification(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
    </script>
</body>
</html>
