<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// التحقق من وجود معرف المستخدم في الجلسة
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    redirect('../pages/login.php');
}

// الحصول على بيانات المستخدم مباشرة من قاعدة البيانات
try {
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();

    if (!$user) {
        // مسح الجلسة إذا لم يتم العثور على المستخدم
        session_destroy();
        redirect('../pages/login.php');
    }

    // إذا كان الحساب مفعل، توجيه إلى لوحة التحكم
    if ($user['status'] === 'active') {
        if ($user['role'] === 'admin') {
            redirect('../admin/index.php');
        } else {
            redirect('../pages/dashboard.php');
        }
    }
} catch (PDOException $e) {
    error_log("Account status error: " . $e->getMessage());
    redirect('../pages/login.php');
}

$pageTitle = 'حالة الحساب';

// إضافة رؤوس الأمان
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - منصة البائعين</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        
        .status-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .status-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .status-body {
            padding: 40px;
            text-align: center;
        }
        
        .status-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .status-pending {
            color: #ffc107;
        }
        
        .status-active {
            color: #28a745;
        }
        
        .status-suspended {
            color: #dc3545;
        }
        
        .status-rejected {
            color: #6c757d;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        
        .btn-home {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: transform 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-home:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 8px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>

<div class="status-container">
    <div class="status-header">
        <h2><i class="fas fa-user-check me-2"></i>حالة الحساب</h2>
        <p class="mb-0 mt-2">مرحباً <?php echo htmlspecialchars($user['full_name']); ?></p>
    </div>
    
    <div class="status-body">
        <?php if ($user['status'] === 'pending'): ?>
            <div class="status-icon status-pending">
                <i class="fas fa-clock"></i>
            </div>
            <h3 class="text-warning">حسابك قيد المراجعة</h3>
            <p class="lead">تم تسجيل حسابك بنجاح! نحن نراجع معلوماتك حالياً.</p>
            <p>سيتم تفعيل حسابك خلال 24-48 ساعة من قبل فريق الإدارة.</p>
            <div class="alert alert-info mt-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>ملاحظة:</strong> لن تتمكن من تسجيل الدخول حتى يتم تفعيل حسابك من قبل الإدارة.
            </div>

        <?php elseif ($user['status'] === 'suspended'): ?>
            <div class="status-icon status-suspended">
                <i class="fas fa-ban"></i>
            </div>
            <h3 class="text-danger">حسابك معلق</h3>
            <p class="lead">تم تعليق حسابك مؤقتاً.</p>
            <p>يرجى التواصل مع الإدارة لمعرفة سبب التعليق وكيفية إعادة التفعيل.</p>
            <div class="alert alert-warning mt-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>تنبيه:</strong> لن تتمكن من الوصول إلى حسابك حتى يتم رفع التعليق.
            </div>

        <?php elseif ($user['status'] === 'rejected'): ?>
            <div class="status-icon status-rejected">
                <i class="fas fa-times-circle"></i>
            </div>
            <h3 class="text-muted">تم رفض طلبك</h3>
            <p class="lead">نأسف، لم يتم قبول طلب التسجيل.</p>
            <p>يمكنك التواصل مع الإدارة لمعرفة الأسباب أو تقديم طلب جديد.</p>
            <div class="alert alert-danger mt-4">
                <i class="fas fa-times-circle me-2"></i>
                <strong>مرفوض:</strong> تم رفض طلب التسجيل الخاص بك نهائياً.
            </div>
        <?php endif; ?>
        
        <div class="info-card">
            <h5><i class="fas fa-info-circle me-2"></i>معلومات الحساب</h5>
            <div class="row text-start">
                <div class="col-md-6">
                    <strong>البريد الإلكتروني:</strong><br>
                    <?php echo htmlspecialchars($user['email']); ?>
                </div>
                <div class="col-md-6">
                    <strong>اسم المتجر:</strong><br>
                    <?php echo htmlspecialchars($user['business_name']); ?>
                </div>
                <div class="col-md-6 mt-2">
                    <strong>المدينة:</strong><br>
                    <?php echo htmlspecialchars($user['city']); ?>
                </div>
                <div class="col-md-6 mt-2">
                    <strong>تاريخ التسجيل:</strong><br>
                    <?php echo date('d/m/Y', strtotime($user['created_at'])); ?>
                </div>
            </div>
        </div>
        
        <?php if ($user['status'] === 'pending'): ?>
            <div class="alert alert-success mt-4">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>نصيحة:</strong> تأكد من أن معلومات التواصل صحيحة حتى نتمكن من الوصول إليك.
            </div>
        <?php endif; ?>

        <div class="mt-4">
            <a href="../index.php" class="btn btn-home">
                <i class="fas fa-home me-2"></i>العودة للصفحة الرئيسية
            </a>
        </div>

        <?php if ($user['status'] === 'rejected'): ?>
        <div class="mt-3">
            <a href="../pages/register.php" class="btn btn-outline-primary">
                <i class="fas fa-user-plus me-2"></i>تقديم طلب جديد
            </a>
        </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <small class="text-muted">
                للاستفسارات: <a href="mailto:<EMAIL>"><EMAIL></a> | 
                <a href="https://wa.me/212612345678">+212 6 12 34 56 78</a>
            </small>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
