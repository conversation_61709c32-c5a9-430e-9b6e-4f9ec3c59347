<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isUserLoggedIn()) {
    redirect('../pages/login.php');
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect('../pages/dashboard.php');
}

$pageTitle = 'إدارة الفئات';

// الحصول على الإحصائيات
$stats = getAdminStats();

$message = '';
$messageType = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        // إضافة فئة جديدة
        $name = sanitizeInput($_POST['name'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $icon = sanitizeInput($_POST['icon'] ?? '');
        $color = sanitizeInput($_POST['color'] ?? '#667eea');
        $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
        $sort_order = (int)($_POST['sort_order'] ?? 0);
        
        if ($name) {
            try {
                // التحقق من عدم تكرار الاسم
                $stmt = $db->prepare("SELECT COUNT(*) FROM categories WHERE name = ?");
                $stmt->execute([$name]);
                
                if ($stmt->fetchColumn() > 0) {
                    $message = 'اسم الفئة موجود مسبقاً';
                    $messageType = 'error';
                } else {
                    $stmt = $db->prepare("
                        INSERT INTO categories (name, description, icon, color, parent_id, sort_order, status, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, 'active', NOW())
                    ");
                    
                    $stmt->execute([$name, $description, $icon, $color, $parent_id, $sort_order]);
                    
                    $message = 'تم إضافة الفئة بنجاح';
                    $messageType = 'success';
                }
            } catch (PDOException $e) {
                $message = 'حدث خطأ في إضافة الفئة';
                $messageType = 'error';
            }
        } else {
            $message = 'يرجى إدخال اسم الفئة';
            $messageType = 'error';
        }
    } elseif ($action === 'edit') {
        // تعديل فئة
        $id = (int)($_POST['category_id'] ?? 0);
        $name = sanitizeInput($_POST['name'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $icon = sanitizeInput($_POST['icon'] ?? '');
        $color = sanitizeInput($_POST['color'] ?? '#667eea');
        $parent_id = !empty($_POST['parent_id']) ? (int)$_POST['parent_id'] : null;
        $sort_order = (int)($_POST['sort_order'] ?? 0);
        
        if ($id > 0 && $name) {
            try {
                // التحقق من عدم تكرار الاسم (باستثناء الفئة الحالية)
                $stmt = $db->prepare("SELECT COUNT(*) FROM categories WHERE name = ? AND id != ?");
                $stmt->execute([$name, $id]);
                
                if ($stmt->fetchColumn() > 0) {
                    $message = 'اسم الفئة موجود مسبقاً';
                    $messageType = 'error';
                } else {
                    $stmt = $db->prepare("
                        UPDATE categories 
                        SET name = ?, description = ?, icon = ?, color = ?, parent_id = ?, sort_order = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    
                    $stmt->execute([$name, $description, $icon, $color, $parent_id, $sort_order, $id]);
                    
                    $message = 'تم تحديث الفئة بنجاح';
                    $messageType = 'success';
                }
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تحديث الفئة';
                $messageType = 'error';
            }
        }
    } elseif ($action === 'toggle_status') {
        $categoryId = (int)($_POST['category_id'] ?? 0);
        $newStatus = $_POST['new_status'] ?? '';
        
        if ($categoryId > 0 && in_array($newStatus, ['active', 'inactive'])) {
            try {
                $stmt = $db->prepare("UPDATE categories SET status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$newStatus, $categoryId]);
                $message = 'تم تحديث حالة الفئة بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تحديث حالة الفئة';
                $messageType = 'error';
            }
        }
    } elseif ($action === 'delete') {
        $categoryId = (int)($_POST['category_id'] ?? 0);
        
        if ($categoryId > 0) {
            try {
                // التحقق من وجود منتجات في هذه الفئة
                $stmt = $db->prepare("SELECT COUNT(*) FROM products WHERE category_id = ?");
                $stmt->execute([$categoryId]);
                $productCount = $stmt->fetchColumn();
                
                // التحقق من وجود فئات فرعية
                $stmt = $db->prepare("SELECT COUNT(*) FROM categories WHERE parent_id = ?");
                $stmt->execute([$categoryId]);
                $subcategoryCount = $stmt->fetchColumn();
                
                if ($productCount > 0) {
                    $message = "لا يمكن حذف الفئة لأنها تحتوي على $productCount منتج";
                    $messageType = 'error';
                } elseif ($subcategoryCount > 0) {
                    $message = "لا يمكن حذف الفئة لأنها تحتوي على $subcategoryCount فئة فرعية";
                    $messageType = 'error';
                } else {
                    $stmt = $db->prepare("DELETE FROM categories WHERE id = ?");
                    $stmt->execute([$categoryId]);
                    $message = 'تم حذف الفئة بنجاح';
                    $messageType = 'success';
                }
            } catch (PDOException $e) {
                $message = 'حدث خطأ في حذف الفئة';
                $messageType = 'error';
            }
        }
    }
}

// جلب الفئات مع عدد المنتجات
try {
    $stmt = $db->prepare("
        SELECT c.*, 
               COUNT(p.id) as product_count,
               parent.name as parent_name
        FROM categories c
        LEFT JOIN products p ON c.id = p.category_id
        LEFT JOIN categories parent ON c.parent_id = parent.id
        GROUP BY c.id
        ORDER BY c.sort_order ASC, c.name ASC
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll();
} catch (PDOException $e) {
    $categories = [];
}

// جلب الفئات الرئيسية للاستخدام في القائمة المنسدلة
try {
    $stmt = $db->prepare("SELECT id, name FROM categories WHERE parent_id IS NULL AND status = 'active' ORDER BY name");
    $stmt->execute();
    $parentCategories = $stmt->fetchAll();
} catch (PDOException $e) {
    $parentCategories = [];
}

// CSS إضافي خاص بهذه الصفحة
$additional_css = '
/* تخصيصات إضافية خاصة بصفحة إدارة الفئات */
.categories-table {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 15px;
}

.category-stats {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.subcategory-indicator {
    padding-right: 20px;
    position: relative;
}

.subcategory-indicator::before {
    content: "└─";
    position: absolute;
    right: 0;
    color: #6c757d;
}

.color-picker {
    width: 50px;
    height: 35px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.icon-preview {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 10px;
}
';

// JavaScript إضافي خاص بهذه الصفحة
$additional_js = '
function editCategory(category) {
    document.getElementById("edit_category_id").value = category.id;
    document.getElementById("edit_name").value = category.name;
    document.getElementById("edit_description").value = category.description || "";
    document.getElementById("edit_icon").value = category.icon || "";
    document.getElementById("edit_color").value = category.color || "#667eea";
    document.getElementById("edit_parent_id").value = category.parent_id || "";
    document.getElementById("edit_sort_order").value = category.sort_order || 0;
    
    updateIconPreview("edit");
    
    const modal = new bootstrap.Modal(document.getElementById("editCategoryModal"));
    modal.show();
}

function updateIconPreview(type) {
    const iconInput = document.getElementById(type + "_icon");
    const colorInput = document.getElementById(type + "_color");
    const preview = document.getElementById(type + "_icon_preview");
    
    if (iconInput && colorInput && preview) {
        const icon = iconInput.value || "fas fa-folder";
        const color = colorInput.value || "#667eea";
        
        preview.style.backgroundColor = color;
        preview.innerHTML = `<i class="${icon}"></i>`;
    }
}

// تحديث معاينة الأيقونة عند تغيير القيم
document.addEventListener("DOMContentLoaded", function() {
    const iconInputs = document.querySelectorAll("input[id$=_icon]");
    const colorInputs = document.querySelectorAll("input[id$=_color]");
    
    iconInputs.forEach(input => {
        input.addEventListener("input", function() {
            const type = this.id.replace("_icon", "");
            updateIconPreview(type);
        });
    });
    
    colorInputs.forEach(input => {
        input.addEventListener("input", function() {
            const type = this.id.replace("_color", "");
            updateIconPreview(type);
        });
    });
    
    // تحديث المعاينة الأولية
    updateIconPreview("add");
});
';

// تضمين header المدير
include 'includes/admin_header.php';
?>

        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="page-title">إدارة الفئات</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus me-2"></i>إضافة فئة جديدة
            </button>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات الفئات -->
        <div class="category-stats">
            <div class="row text-center">
                <div class="col-md-3">
                    <h4 class="text-primary"><?php echo count($categories); ?></h4>
                    <small class="text-muted">إجمالي الفئات</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-success"><?php echo count(array_filter($categories, fn($c) => $c['status'] === 'active')); ?></h4>
                    <small class="text-muted">فئات نشطة</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-info"><?php echo count(array_filter($categories, fn($c) => $c['parent_id'] === null)); ?></h4>
                    <small class="text-muted">فئات رئيسية</small>
                </div>
                <div class="col-md-3">
                    <h4 class="text-warning"><?php echo array_sum(array_column($categories, 'product_count')); ?></h4>
                    <small class="text-muted">إجمالي المنتجات</small>
                </div>
            </div>
        </div>

        <!-- جدول الفئات -->
        <div class="categories-table">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الفئة</th>
                            <th>الوصف</th>
                            <th>الفئة الرئيسية</th>
                            <th>عدد المنتجات</th>
                            <th>ترتيب العرض</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($categories)): ?>
                            <?php foreach ($categories as $category): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center <?php echo $category['parent_id'] ? 'subcategory-indicator' : ''; ?>">
                                            <div class="icon-preview me-2" style="background-color: <?php echo htmlspecialchars($category['color']); ?>">
                                                <i class="<?php echo htmlspecialchars($category['icon'] ?: 'fas fa-folder'); ?>"></i>
                                            </div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($category['name']); ?></strong>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars(substr($category['description'] ?: 'لا يوجد وصف', 0, 50)); ?>
                                            <?php if (strlen($category['description'] ?: '') > 50) echo '...'; ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($category['parent_name']): ?>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($category['parent_name']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">فئة رئيسية</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-info"><?php echo $category['product_count']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark"><?php echo $category['sort_order']; ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $category['status'] === 'active' ? 'success' : 'danger'; ?>">
                                            <?php echo $category['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" 
                                                    onclick="editCategory(<?php echo htmlspecialchars(json_encode($category)); ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                                <input type="hidden" name="new_status" value="<?php echo $category['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                                <button type="submit" class="btn btn-sm btn-<?php echo $category['status'] === 'active' ? 'warning' : 'success'; ?>">
                                                    <i class="fas fa-<?php echo $category['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                                </button>
                                            </form>
                                            
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger" 
                                                        onclick="return confirm('هل أنت متأكد من حذف هذه الفئة؟')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد فئات</h5>
                                    <p class="text-muted">ابدأ بإضافة فئة جديدة</p>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

<!-- نموذج إضافة فئة جديدة -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة فئة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="mb-3">
                        <label class="form-label">اسم الفئة *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">أيقونة الفئة</label>
                                <input type="text" class="form-control" id="add_icon" name="icon"
                                       placeholder="fas fa-folder" value="fas fa-folder">
                                <small class="text-muted">استخدم أيقونات Font Awesome</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">اللون</label>
                                <input type="color" class="form-control color-picker" id="add_color" name="color" value="#667eea">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">معاينة الأيقونة</label>
                        <div id="add_icon_preview" class="icon-preview" style="background-color: #667eea;">
                            <i class="fas fa-folder"></i>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الفئة الرئيسية</label>
                                <select class="form-control" name="parent_id">
                                    <option value="">فئة رئيسية</option>
                                    <?php foreach ($parentCategories as $parent): ?>
                                        <option value="<?php echo $parent['id']; ?>">
                                            <?php echo htmlspecialchars($parent['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" name="sort_order" value="0" min="0">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة الفئة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نموذج تعديل فئة -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل الفئة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" id="edit_category_id" name="category_id">

                    <div class="mb-3">
                        <label class="form-label">اسم الفئة *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">أيقونة الفئة</label>
                                <input type="text" class="form-control" id="edit_icon" name="icon" placeholder="fas fa-folder">
                                <small class="text-muted">استخدم أيقونات Font Awesome</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">اللون</label>
                                <input type="color" class="form-control color-picker" id="edit_color" name="color">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">معاينة الأيقونة</label>
                        <div id="edit_icon_preview" class="icon-preview">
                            <i class="fas fa-folder"></i>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الفئة الرئيسية</label>
                                <select class="form-control" id="edit_parent_id" name="parent_id">
                                    <option value="">فئة رئيسية</option>
                                    <?php foreach ($parentCategories as $parent): ?>
                                        <option value="<?php echo $parent['id']; ?>">
                                            <?php echo htmlspecialchars($parent['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">ترتيب العرض</label>
                                <input type="number" class="form-control" id="edit_sort_order" name="sort_order" min="0">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// تضمين footer المدير
include 'includes/admin_footer.php';
?>
