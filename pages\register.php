<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    // إعدادات الأمان للجلسة
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        // تعيين session.cookie_secure فقط إذا كان الاتصال آمن
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل ومفعل
if (isUserLoggedIn()) {
    $user = getCurrentUser();
    if ($user && $user['status'] === 'active') {
        if ($user['role'] === 'admin') {
            redirect('../admin/index.php');
        } else {
            redirect('../pages/dashboard.php');
        }
    }
}

$pageTitle = 'تسجيل بائع جديد';
$errors = [];
$success = '';

// التحقق من CSRF Token - الدوال موجودة في security.php

// حماية من Rate Limiting
function checkRateLimit() {
    $ip = $_SERVER['REMOTE_ADDR'];
    $current_time = time();

    if (!isset($_SESSION['rate_limit'])) {
        $_SESSION['rate_limit'] = [];
    }

    // تنظيف المحاولات القديمة (أكثر من 15 دقيقة)
    $_SESSION['rate_limit'] = array_filter($_SESSION['rate_limit'], function($timestamp) use ($current_time) {
        return ($current_time - $timestamp) < 900; // 15 دقيقة
    });

    // التحقق من عدد المحاولات
    if (count($_SESSION['rate_limit']) >= 5) {
        return false;
    }

    return true;
}

// إضافة محاولة جديدة
function addRateLimit() {
    $_SESSION['rate_limit'][] = time();
}

// تم نقل دالة sanitizeInputAdvanced إلى ملف functions.php

// تم نقل دالة validatePasswordStrength إلى ملف security.php كـ validatePassword

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // التحقق من Rate Limiting
    if (!checkRateLimit()) {
        $errors[] = 'تم تجاوز الحد المسموح من المحاولات. يرجى المحاولة بعد 15 دقيقة.';
    } else {
        // إضافة محاولة جديدة
        addRateLimit();

        // التحقق من CSRF Token (مُعطل مؤقتاً للاختبار)
        // if (!checkCSRFToken($_POST['csrf_token'] ?? '')) {
        //     $errors[] = 'رمز الأمان غير صحيح. يرجى إعادة تحميل الصفحة.';
        // } else {
        if (true) {
            // تنظيف البيانات بطريقة آمنة
            $email = filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL);
            $password = $_POST['password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';
            $full_name = sanitizeInputAdvanced($_POST['full_name'] ?? '');
            $phone = sanitizeInputAdvanced($_POST['phone'] ?? '');
            $city = sanitizeInputAdvanced($_POST['city'] ?? '');
            $business_name = sanitizeInputAdvanced($_POST['business_name'] ?? '');
            $bank_account = preg_replace('/[^0-9]/', '', $_POST['bank_account'] ?? '');
            $bank_name = sanitizeInputAdvanced($_POST['bank_name'] ?? '');
            $other_bank_name = sanitizeInputAdvanced($_POST['other_bank_name'] ?? '');

            // حماية Honeypot - حقل مخفي يجب أن يبقى فارغ
            $honeypot = $_POST['website'] ?? '';
            if (!empty($honeypot)) {
                // هذا بوت، تجاهل الطلب
                logSecurityEvent('bot_detected', [
                    'honeypot_value' => $honeypot,
                    'email' => $email,
                    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
                ]);
                $errors[] = 'حدث خطأ في التحقق من البيانات';
            }

            // التحقق من الوقت المستغرق لملء النموذج (حماية من البوتات السريعة)
            $form_start_time = $_POST['form_start_time'] ?? 0;
            $current_time = time();
            $time_taken = $current_time - $form_start_time;

            if ($time_taken < 10) { // أقل من 10 ثواني
                $errors[] = 'يرجى أخذ وقت كافي لملء النموذج بعناية';
            }

            // إذا تم اختيار "أخرى" استخدم اسم البنك المكتوب
            if ($bank_name === 'other' && !empty($other_bank_name)) {
                $bank_name = $other_bank_name;
            }
    
            // التحقق من صحة البيانات مع حماية متقدمة
            if (empty($email)) {
                $errors[] = 'البريد الإلكتروني مطلوب';
            } elseif (!validateEmailAdvanced($email)) {
                $errors[] = 'البريد الإلكتروني غير صحيح أو من نطاق محظور';
            }

            if (empty($password)) {
                $errors[] = 'كلمة المرور مطلوبة';
            } else {
                $passwordValidation = validatePassword($password);
                if (!$passwordValidation['valid']) {
                    $errors[] = $passwordValidation['message'];
                }
            }

            if ($password !== $confirm_password) {
                $errors[] = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتان';
            }
    
            if (empty($full_name)) {
                $errors[] = 'الاسم الكامل مطلوب';
            } elseif (strlen($full_name) < 2 || strlen($full_name) > 100) {
                $errors[] = 'الاسم الكامل يجب أن يكون بين 2 و 100 حرف';
            } elseif (!preg_match('/^[\p{Arabic}\p{L}\s\-\.]+$/u', $full_name)) {
                $errors[] = 'الاسم الكامل يحتوي على أحرف غير مسموحة';
            } elseif (preg_match('/(script|javascript|vbscript|onload|onerror|onclick)/i', $full_name)) {
                $errors[] = 'الاسم الكامل يحتوي على محتوى غير مسموح';
            }

            if (empty($phone)) {
                $errors[] = 'رقم الواتساب مطلوب';
            } elseif (!validatePhone($phone)) {
                $errors[] = 'رقم الواتساب غير صحيح';
            }

            if (empty($business_name)) {
                $errors[] = 'اسم المتجر مطلوب';
            } elseif (strlen($business_name) < 2 || strlen($business_name) > 100) {
                $errors[] = 'اسم المتجر يجب أن يكون بين 2 و 100 حرف';
            } elseif (!preg_match('/^[\p{Arabic}\p{L}\s\d\-_\.]+$/u', $business_name)) {
                $errors[] = 'اسم المتجر يحتوي على أحرف غير مسموحة';
            } elseif (preg_match('/(script|javascript|vbscript|onload|onerror|onclick|select|insert|update|delete|drop|union)/i', $business_name)) {
                $errors[] = 'اسم المتجر يحتوي على محتوى غير مسموح';
            } elseif (preg_match('/^(admin|administrator|root|system|test|demo|null|undefined)$/i', $business_name)) {
                $errors[] = 'اسم المتجر غير متاح. يرجى اختيار اسم آخر';
            }

            if (empty($city)) {
                $errors[] = 'المدينة مطلوبة';
            } elseif (strlen($city) < 2 || strlen($city) > 50) {
                $errors[] = 'اسم المدينة يجب أن يكون بين 2 و 50 حرف';
            } elseif (!preg_match('/^[\p{Arabic}\p{L}\s\-]+$/u', $city)) {
                $errors[] = 'اسم المدينة يحتوي على أحرف غير مسموحة';
            } elseif (preg_match('/(script|javascript|vbscript|onload|onerror|onclick)/i', $city)) {
                $errors[] = 'اسم المدينة يحتوي على محتوى غير مسموح';
            }

            if (empty($bank_account)) {
                $errors[] = 'رقم الحساب البنكي مطلوب';
            } elseif (!validateBankAccount($bank_account)) {
                $errors[] = 'رقم الحساب البنكي يجب أن يكون 24 رقم بالضبط';
            }

            if (empty($bank_name)) {
                $errors[] = 'اسم البنك مطلوب';
            } elseif ($bank_name === 'other' && empty($other_bank_name)) {
                $errors[] = 'يرجى كتابة اسم البنك';
            } elseif ($bank_name === 'other' && strlen($other_bank_name) > 100) {
                $errors[] = 'اسم البنك طويل جداً';
            } elseif ($bank_name === 'other' && !preg_match('/^[\p{Arabic}\p{L}\s\-\.]+$/u', $other_bank_name)) {
                $errors[] = 'اسم البنك يحتوي على أحرف غير مسموحة';
            } elseif ($bank_name === 'other' && preg_match('/(script|javascript|vbscript|onload|onerror|onclick)/i', $other_bank_name)) {
                $errors[] = 'اسم البنك يحتوي على محتوى غير مسموح';
            }
    
            // التحقق من عدم وجود البريد الإلكتروني أو اسم المتجر مسبقاً
            if (empty($errors)) {
                try {
                    // التحقق من البريد الإلكتروني
                    $stmt = $db->prepare("SELECT id, email FROM users WHERE email = ?");
                    $stmt->execute([$email]);
                    if ($stmt->fetch()) {
                        // تسجيل محاولة تسجيل بريد مكرر
                        logSecurityEvent('duplicate_email_attempt', [
                            'email' => $email,
                            'business_name' => $business_name
                        ]);
                        $errors[] = 'البريد الإلكتروني مستخدم بالفعل. يرجى استخدام بريد إلكتروني آخر.';
                    }

                    // التحقق من اسم المتجر
                    $stmt = $db->prepare("SELECT id, business_name FROM users WHERE business_name = ?");
                    $stmt->execute([$business_name]);
                    if ($stmt->fetch()) {
                        // تسجيل محاولة تسجيل اسم متجر مكرر
                        logSecurityEvent('duplicate_business_name_attempt', [
                            'email' => $email,
                            'business_name' => $business_name
                        ]);
                        $errors[] = 'اسم المتجر مستخدم بالفعل. يرجى اختيار اسم متجر آخر.';
                    }

                    // التحقق من رقم الحساب البنكي
                    $stmt = $db->prepare("SELECT id, bank_account FROM users WHERE bank_account = ?");
                    $stmt->execute([$bank_account]);
                    if ($stmt->fetch()) {
                        $errors[] = 'رقم الحساب البنكي مستخدم بالفعل. يرجى التأكد من رقم الحساب.';
                    }
                } catch (PDOException $e) {
                    error_log("Database error: " . $e->getMessage());
                    $errors[] = 'حدث خطأ في التحقق من البيانات';
                }
            }

            // إدراج البائع الجديد
            if (empty($errors)) {
                try {
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                    $stmt = $db->prepare("
                        INSERT INTO users (email, password, full_name, phone, city, business_name, bank_account, bank_name, role, status, created_at, ip_address)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'seller', 'pending', NOW(), ?)
                    ");

                    $stmt->execute([
                        $email, $hashedPassword, $full_name, $phone,
                        $city, $business_name, $bank_account, $bank_name, $_SERVER['REMOTE_ADDR']
                    ]);

                    // تسجيل حدث التسجيل الناجح
                    logSecurityEvent('user_registered', [
                        'email' => $email,
                        'business_name' => $business_name,
                        'city' => $city
                    ]);

                    $success = 'تم تسجيل حسابك بنجاح! سيتم مراجعة طلبك وتفعيل حسابك خلال 24-48 ساعة من قبل فريق الإدارة. لن تتمكن من تسجيل الدخول حتى يتم تفعيل حسابك.';

                    // مسح البيانات من النموذج
                    $email = $full_name = $phone = $city = $business_name = $bank_account = $bank_name = '';

                    // إعادة تعيين CSRF Token
                    renewCSRFToken();

                } catch (PDOException $e) {
                    error_log("Database error: " . $e->getMessage());

                    // تسجيل خطأ التسجيل
                    logSecurityEvent('registration_error', [
                        'email' => $email,
                        'error' => $e->getMessage()
                    ]);

                    $errors[] = 'حدث خطأ في إنشاء الحساب. يرجى المحاولة مرة أخرى.';
                }
            }
        }
    }
}

?>

<?php
// إضافة رؤوس الأمان
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' cdn.jsdelivr.net cdnjs.cloudflare.com; style-src \'self\' \'unsafe-inline\' cdn.jsdelivr.net cdnjs.cloudflare.com fonts.googleapis.com; font-src \'self\' fonts.googleapis.com fonts.gstatic.com cdnjs.cloudflare.com; img-src \'self\' data:; connect-src \'self\';');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle, ENT_QUOTES, 'UTF-8'); ?> - منصة البائعين</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .register-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
        }

        .register-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .register-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .register-body {
            padding: 40px;
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 15px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .valid-feedback {
            display: block;
            color: #28a745;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .is-valid {
            border-color: #28a745;
        }

        .is-invalid {
            border-color: #dc3545;
        }

        .password-strength {
            font-size: 0.875rem;
            font-weight: 600;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .strength-0, .strength-1 {
            background-color: #f8d7da;
            color: #721c24;
        }

        .strength-2, .strength-3 {
            background-color: #fff3cd;
            color: #856404;
        }

        .strength-4, .strength-5 {
            background-color: #d1edff;
            color: #0c5460;
        }

        /* حماية من النسخ */
        .no-select {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    </style>
</head>
<body>

<a href="../index.php" class="back-link">
    <i class="fas fa-arrow-right me-2"></i>العودة للرئيسية
</a>

<div class="register-container">
    <div class="register-header">
        <h2><i class="fas fa-user-plus me-2"></i>إنشاء حساب</h2>
        <p class="mb-0 mt-2">انضم إلى منصتنا وابدأ رحلتك في التجارة الإلكترونية</p>
    </div>

    <div class="register-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate autocomplete="off">
                        <!-- CSRF Token -->
                        <?php echo csrfField(); ?>

                        <!-- Honeypot Field - يجب أن يبقى فارغ -->
                        <input type="text" name="website" style="display: none !important;" tabindex="-1" autocomplete="off">

                        <!-- Form Start Time -->
                        <input type="hidden" name="form_start_time" value="<?php echo time(); ?>">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name"
                                           value="<?php echo htmlspecialchars($full_name ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                           maxlength="100" required autocomplete="name"
                                           pattern="^[\p{Arabic}\p{L}\s\-\.]+$"
                                           title="يرجى إدخال اسم صحيح باستخدام الأحرف فقط">
                                    <div class="invalid-feedback">
                                        يرجى إدخال الاسم الكامل
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="business_name" class="form-label">اسم المتجر *</label>
                                    <input type="text" class="form-control" id="business_name" name="business_name"
                                           value="<?php echo htmlspecialchars($business_name ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                           maxlength="100" required autocomplete="organization"
                                           pattern="^[\p{Arabic}\p{L}\s\d\-_\.]+$"
                                           title="يرجى إدخال اسم متجر صحيح">
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم المتجر
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo htmlspecialchars($email ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                   maxlength="255" required autocomplete="email"
                                   pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
                                   title="يرجى إدخال بريد إلكتروني صحيح">
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" id="password" name="password"
                                           minlength="8" required autocomplete="new-password">
                                    <div id="password-strength" class="password-strength mt-1"></div>
                                    <small class="form-text text-muted">
                                        يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، رقم، ورمز خاص
                                    </small>
                                    <div class="invalid-feedback">
                                        كلمة المرور يجب أن تكون 8 أحرف على الأقل مع حرف كبير وصغير ورقم ورمز خاص
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                           minlength="6" required>
                                    <div class="invalid-feedback">
                                        يرجى تأكيد كلمة المرور
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الواتساب *</label>
                                    <input type="tel" class="form-control" id="phone" name="phone"
                                           value="<?php echo htmlspecialchars($phone ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                           maxlength="15"
                                           placeholder="مثال: +212612345678" required autocomplete="tel"
                                           pattern="^\+?[0-9]{10,15}$"
                                           title="يرجى إدخال رقم هاتف صحيح (10-15 رقم)">
                                    <div class="invalid-feedback">
                                        يرجى إدخال رقم الواتساب
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="city" class="form-label">المدينة *</label>
                                    <input type="text" class="form-control" id="city" name="city"
                                           value="<?php echo htmlspecialchars($city ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                           maxlength="50" required autocomplete="address-level2"
                                           pattern="^[\p{Arabic}\p{L}\s\-]+$"
                                           title="يرجى إدخال اسم مدينة صحيح">
                                    <div class="invalid-feedback">
                                        يرجى إدخال المدينة
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bank_account" class="form-label">رقم الحساب البنكي (RIB) *</label>
                            <input type="text" class="form-control" id="bank_account" name="bank_account"
                                   value="<?php echo htmlspecialchars($bank_account ?? '', ENT_QUOTES, 'UTF-8'); ?>"
                                   pattern="[0-9]{24}" maxlength="24" minlength="24"
                                   placeholder="أدخل رقم الحساب البنكي (RIB) - 24 رقم" required autocomplete="off"
                                   inputmode="numeric"
                                   title="يرجى إدخال رقم حساب بنكي صحيح (24 رقم بالضبط)">
                            <div class="invalid-feedback">
                                يرجى إدخال رقم حساب بنكي صحيح (24 رقم بالضبط)
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="bank_name" class="form-label">اسم البنك *</label>
                            <select class="form-control" id="bank_name" name="bank_name" required onchange="toggleOtherBank()">
                                <option value="">Choisir la banque</option>
                                <option value="CIH Bank" <?php echo ($bank_name ?? '') == 'CIH Bank' ? 'selected' : ''; ?>>CIH Bank</option>
                                <option value="other" <?php echo ($bank_name ?? '') == 'other' ? 'selected' : ''; ?>>Autre</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار البنك
                            </div>
                        </div>

                        <div class="mb-3" id="other_bank_div" style="display: none;">
                            <label for="other_bank_name" class="form-label">اكتب اسم البنك *</label>
                            <input type="text" class="form-control" id="other_bank_name" name="other_bank_name"
                                   placeholder="أدخل اسم البنك" maxlength="100"
                                   pattern="^[\p{Arabic}\p{L}\s\-\.]+$"
                                   title="يرجى إدخال اسم بنك صحيح">
                            <div class="invalid-feedback">
                                يرجى إدخال اسم البنك
                            </div>
                        </div>
                        
                        <div class="mb-4 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="terms.php" target="_blank" class="text-decoration-none">الشروط والأحكام</a> و <a href="privacy.php" target="_blank" class="text-decoration-none">سياسة الخصوصية</a>
                            </label>
                            <div class="invalid-feedback">
                                يجب الموافقة على الشروط والأحكام
                            </div>
                        </div>

                        <div class="d-grid gap-2 mb-4">
                            <button type="submit" class="btn btn-register btn-lg text-white">
                                <i class="fas fa-user-plus me-2"></i>إنشاء حساب
                            </button>
                        </div>
                    </form>

                    <div class="text-center">
                        <p class="text-muted">لديك حساب بالفعل؟ <a href="login.php" class="text-decoration-none fw-bold">تسجيل الدخول</a></p>
                    </div>
                </div>
            </div>

<script>
// إظهار/إخفاء حقل البنك الآخر
function toggleOtherBank() {
    const bankSelect = document.getElementById('bank_name');
    const otherBankDiv = document.getElementById('other_bank_div');
    const otherBankInput = document.getElementById('other_bank_name');

    if (bankSelect.value === 'other') {
        otherBankDiv.style.display = 'block';
        otherBankInput.required = true;
    } else {
        otherBankDiv.style.display = 'none';
        otherBankInput.required = false;
        otherBankInput.value = '';
    }
}

// تشغيل الدالة عند تحميل الصفحة للتحقق من القيمة المحفوظة
document.addEventListener('DOMContentLoaded', function() {
    toggleOtherBank();
});

// التحقق من تطابق كلمة المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// التحقق من قوة كلمة المرور المحسن
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthIndicator = document.getElementById('password-strength');

    // التحقق من قوة كلمة المرور
    let strength = 0;
    let message = '';

    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    if (password.length < 8) {
        this.setCustomValidity('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
        message = 'ضعيفة جداً';
    } else if (strength < 4) {
        this.setCustomValidity('كلمة المرور ضعيفة. يجب أن تحتوي على حرف كبير وصغير ورقم ورمز خاص');
        message = 'ضعيفة';
    } else if (strength === 4) {
        this.setCustomValidity('');
        message = 'متوسطة';
    } else {
        this.setCustomValidity('');
        message = 'قوية';
    }

    // عرض مؤشر قوة كلمة المرور
    if (strengthIndicator) {
        strengthIndicator.textContent = 'قوة كلمة المرور: ' + message;
        strengthIndicator.className = 'password-strength strength-' + strength;
    }
});

// منع النسخ واللصق في حقول كلمة المرور
const passwordField = document.getElementById('password');
const confirmPasswordField = document.getElementById('confirm_password');

if (passwordField) {
    passwordField.addEventListener('paste', function(e) {
        e.preventDefault();
    });
}

if (confirmPasswordField) {
    confirmPasswordField.addEventListener('paste', function(e) {
        e.preventDefault();
    });
}

// حماية من التلاعب بالنموذج
document.addEventListener('DOMContentLoaded', function() {
    // منع النسخ واللصق في حقول حساسة
    const sensitiveFields = ['bank_account'];
    sensitiveFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('paste', function(e) {
                e.preventDefault();
                alert('لا يمكن لصق النص في هذا الحقل لأسباب أمنية');
            });
        }
    });

    // منع فتح أدوات المطور
    document.addEventListener('keydown', function(e) {
        if (e.key === 'F12' ||
            (e.ctrlKey && e.shiftKey && e.key === 'I') ||
            (e.ctrlKey && e.shiftKey && e.key === 'C') ||
            (e.ctrlKey && e.key === 'U')) {
            e.preventDefault();
            return false;
        }
    });

    // منع النقر بالزر الأيمن
    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        return false;
    });
});

// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                // التحقق من الحقل المخفي (Honeypot)
                const honeypot = document.querySelector('input[name="website"]');
                if (honeypot && honeypot.value !== '') {
                    event.preventDefault();
                    alert('تم اكتشاف نشاط مشبوه');
                    return false;
                }

                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// التحقق من رقم الحساب البنكي في الوقت الفعلي
document.getElementById('bank_account').addEventListener('input', function() {
    const value = this.value.replace(/[^0-9]/g, ''); // إزالة أي شيء غير الأرقام
    this.value = value; // تحديث القيمة

    const feedback = this.parentNode.querySelector('.invalid-feedback');
    const isValid = value.length === 24;

    if (value.length > 0) {
        if (isValid) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
            feedback.textContent = 'رقم حساب بنكي صحيح ✓';
            feedback.className = 'valid-feedback';
        } else {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            feedback.textContent = `يجب أن يكون 24 رقم (حالياً: ${value.length} رقم)`;
            feedback.className = 'invalid-feedback';
        }
    } else {
        this.classList.remove('is-valid', 'is-invalid');
        feedback.textContent = 'يرجى إدخال رقم حساب بنكي صحيح (24 رقم بالضبط)';
        feedback.className = 'invalid-feedback';
    }
});
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
