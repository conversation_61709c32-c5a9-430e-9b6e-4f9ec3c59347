<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isUserLoggedIn()) {
    redirect('../pages/login.php');
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect('../pages/dashboard.php');
}

$pageTitle = 'إدارة البائعين';

// معالجة الإجراءات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $userId = (int)($_POST['user_id'] ?? 0);

    if ($action && $userId) {
        try {
            $db = getDBConnection();

            switch ($action) {
                case 'approve':
                    $stmt = $db->prepare("UPDATE users SET status = 'active', updated_at = NOW() WHERE id = ? AND role = 'seller'");
                    $stmt->execute([$userId]);
                    $message = 'تم قبول البائع بنجاح!';
                    $messageType = 'success';
                    break;

                case 'reject':
                case 'deactivate':
                    $stmt = $db->prepare("UPDATE users SET status = 'inactive', updated_at = NOW() WHERE id = ? AND role = 'seller'");
                    $stmt->execute([$userId]);
                    $message = $action === 'reject' ? 'تم رفض البائع!' : 'تم تعطيل البائع!';
                    $messageType = 'warning';
                    break;

                case 'activate':
                    $stmt = $db->prepare("UPDATE users SET status = 'active', updated_at = NOW() WHERE id = ? AND role = 'seller'");
                    $stmt->execute([$userId]);
                    $message = 'تم تفعيل البائع بنجاح!';
                    $messageType = 'success';
                    break;

                case 'delete':
                    // التحقق من عدم وجود طلبات للبائع
                    $stmt = $db->prepare("SELECT COUNT(*) FROM orders WHERE seller_id = ?");
                    $stmt->execute([$userId]);
                    $orderCount = $stmt->fetchColumn();

                    if ($orderCount > 0) {
                        $message = 'لا يمكن حذف البائع لأنه يحتوي على طلبات!';
                        $messageType = 'danger';
                    } else {
                        $stmt = $db->prepare("DELETE FROM users WHERE id = ? AND role = 'seller'");
                        $stmt->execute([$userId]);
                        $message = 'تم حذف البائع نهائياً!';
                        $messageType = 'info';
                    }
                    break;
            }
        } catch (Exception $e) {
            $message = 'حدث خطأ: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// معاملات البحث والفلترة
$statusFilter = $_GET['status'] ?? '';
$searchTerm = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// بناء استعلام البحث
$whereConditions = ["role = 'seller'"];
$params = [];

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

if ($searchTerm) {
    $whereConditions[] = "(full_name LIKE ? OR store_name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $searchParam = "%$searchTerm%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam]);
}

$whereClause = implode(' AND ', $whereConditions);

// الحصول على البائعين
try {
    $db = getDBConnection();

    // عدد البائعين الإجمالي
    $countStmt = $db->prepare("SELECT COUNT(*) FROM users WHERE $whereClause");
    $countStmt->execute($params);
    $totalSellers = $countStmt->fetchColumn();
    $totalPages = ceil($totalSellers / $limit);

    // البائعين للصفحة الحالية
    $stmt = $db->prepare("
        SELECT u.*,
               COUNT(o.id) as total_orders,
               COUNT(CASE WHEN o.status = 'delivered' THEN 1 END) as completed_orders,
               SUM(CASE WHEN o.status = 'delivered' THEN o.total_amount ELSE 0 END) as total_sales
        FROM users u
        LEFT JOIN orders o ON u.id = o.seller_id
        WHERE $whereClause
        GROUP BY u.id
        ORDER BY u.created_at DESC
        LIMIT $limit OFFSET $offset
    ");
    $stmt->execute($params);
    $sellers = $stmt->fetchAll();

} catch (Exception $e) {
    $sellers = [];
    $totalSellers = 0;
    $totalPages = 0;
    $message = 'خطأ في جلب البيانات: ' . $e->getMessage();
    $messageType = 'danger';
}

// الحصول على الإحصائيات
$stats = getAdminStats();

// CSS إضافي خاص بهذه الصفحة
$additional_css = '
/* تخصيصات إضافية خاصة بصفحة إدارة البائعين */
.users-table {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}

.action-btn {
    padding: 5px 10px;
    border-radius: 5px;
    border: none;
    font-size: 0.8rem;
    margin: 2px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-approve {
    background: #28a745;
    color: white;
}

.btn-reject {
    background: #dc3545;
    color: white;
}

.btn-view {
    background: #17a2b8;
    color: white;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* تحسينات إضافية */
.user-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.seller-stats {
    font-size: 0.85rem;
}

.seller-stats strong {
    font-weight: 600;
}

.table td {
    vertical-align: middle;
    padding: 12px 8px;
}

.table th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 4px 8px;
    font-size: 0.8rem;
}

.alert {
    border-radius: 10px;
    border: none;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #e0e6ed;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.empty-state {
    padding: 60px 20px;
    text-align: center;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
}

.search-highlight {
    background: yellow;
    padding: 1px 3px;
    border-radius: 3px;
}
';

// JavaScript إضافي خاص بهذه الصفحة
$additional_js = '
// دوال إدارة البائعين
function performAction(action, userId, userName) {
    let confirmMessage = "";

    switch(action) {
        case "approve":
            confirmMessage = "هل أنت متأكد من الموافقة على البائع: " + userName + "؟";
            break;
        case "reject":
            confirmMessage = "هل أنت متأكد من رفض البائع: " + userName + "؟";
            break;
        case "deactivate":
            confirmMessage = "هل أنت متأكد من تعطيل البائع: " + userName + "؟";
            break;
        case "activate":
            confirmMessage = "هل أنت متأكد من تفعيل البائع: " + userName + "؟";
            break;
        case "delete":
            confirmMessage = "هل أنت متأكد من حذف البائع: " + userName + " نهائياً؟\\nهذا الإجراء لا يمكن التراجع عنه!";
            break;
        default:
            return;
    }

    if (confirm(confirmMessage)) {
        // إنشاء نموذج وإرساله
        const form = document.createElement("form");
        form.method = "POST";
        form.style.display = "none";

        const actionInput = document.createElement("input");
        actionInput.type = "hidden";
        actionInput.name = "action";
        actionInput.value = action;

        const userIdInput = document.createElement("input");
        userIdInput.type = "hidden";
        userIdInput.name = "user_id";
        userIdInput.value = userId;

        form.appendChild(actionInput);
        form.appendChild(userIdInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function viewUser(userId) {
    // فتح نافذة تفاصيل البائع
    window.open("user_details.php?id=" + userId, "_blank", "width=900,height=700");
}

function filterSellers() {
    const status = document.getElementById("statusFilter").value;
    const search = document.getElementById("searchInput").value;

    let url = "users.php?";
    const params = [];

    if (status) params.push("status=" + encodeURIComponent(status));
    if (search) params.push("search=" + encodeURIComponent(search));

    url += params.join("&");
    window.location.href = url;
}

// البحث التلقائي
let searchTimeout;
function autoSearch() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(filterSellers, 500);
}

// تحديث الصفحة كل 30 ثانية للحصول على أحدث البيانات
setInterval(function() {
    // يمكن إضافة تحديث AJAX هنا بدلاً من إعادة تحميل الصفحة
}, 30000);
';

// تضمين header المدير
include 'includes/admin_header.php';
?>

        <h1 class="page-title">إدارة البائعين</h1>
        <p class="welcome-text">إدارة وموافقة البائعين الجدد</p>

        <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : ($messageType === 'danger' ? 'exclamation-triangle' : 'info-circle'); ?> me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <!-- إحصائيات سريعة -->
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['sellers']['total'] ?? 0; ?></div>
                    <div class="stats-label">إجمالي البائعين</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon users" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['sellers']['active'] ?? 0; ?></div>
                    <div class="stats-label">البائعين النشطين</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon users" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['sellers']['pending'] ?? 0; ?></div>
                    <div class="stats-label">في انتظار الموافقة</div>
                </div>
            </div>
            
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-icon users" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);">
                        <i class="fas fa-times-circle"></i>
                    </div>
                    <div class="stats-number"><?php echo $stats['sellers']['inactive'] ?? 0; ?></div>
                    <div class="stats-label">البائعين المعطلين</div>
                </div>
            </div>
        </div>
        
        <!-- فلاتر البحث -->
        <div class="users-table">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5><i class="fas fa-users me-2"></i>قائمة البائعين (<?php echo $totalSellers; ?> بائع)</h5>
                <div class="d-flex gap-2">
                    <input type="text"
                           class="form-control form-control-sm"
                           id="searchInput"
                           placeholder="البحث بالاسم، المتجر، البريد أو الهاتف..."
                           value="<?php echo htmlspecialchars($searchTerm); ?>"
                           onkeyup="autoSearch()"
                           style="width: 300px;">

                    <select class="form-select form-select-sm"
                            id="statusFilter"
                            onchange="filterSellers()"
                            style="width: auto;">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>نشط</option>
                        <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>في انتظار الموافقة</option>
                        <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>معطل</option>
                    </select>

                    <button class="btn btn-success btn-sm" onclick="window.location.href='add_seller.php'">
                        <i class="fas fa-plus me-1"></i>إضافة بائع
                    </button>

                    <button class="btn btn-info btn-sm" onclick="window.location.reload()">
                        <i class="fas fa-sync me-1"></i>تحديث
                    </button>
                </div>
            </div>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>البائع</th>
                            <th>معلومات الاتصال</th>
                            <th>الحالة</th>
                            <th>الإحصائيات</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($sellers)): ?>
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد بائعين مطابقين للبحث</p>
                                <?php if ($searchTerm || $statusFilter): ?>
                                <button class="btn btn-outline-primary btn-sm" onclick="window.location.href='users.php'">
                                    <i class="fas fa-undo me-1"></i>إظهار جميع البائعين
                                </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($sellers as $seller): ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-3">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0"><?php echo htmlspecialchars($seller['full_name']); ?></h6>
                                        <small class="text-muted"><?php echo htmlspecialchars($seller['store_name'] ?: 'لم يحدد اسم المتجر'); ?></small>
                                        <?php if ($seller['commission_rate']): ?>
                                        <div><small class="badge bg-info">عمولة: <?php echo $seller['commission_rate']; ?>%</small></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <small class="d-block"><i class="fas fa-envelope me-1"></i><?php echo htmlspecialchars($seller['email']); ?></small>
                                    <?php if ($seller['phone']): ?>
                                    <small class="d-block"><i class="fas fa-phone me-1"></i><?php echo htmlspecialchars($seller['phone']); ?></small>
                                    <?php endif; ?>
                                    <?php if ($seller['whatsapp']): ?>
                                    <small class="d-block"><i class="fab fa-whatsapp me-1"></i><?php echo htmlspecialchars($seller['whatsapp']); ?></small>
                                    <?php endif; ?>
                                    <?php if ($seller['city']): ?>
                                    <small class="d-block"><i class="fas fa-map-marker-alt me-1"></i><?php echo htmlspecialchars($seller['city']); ?></small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php
                                $statusClass = '';
                                $statusText = '';
                                switch ($seller['status']) {
                                    case 'active':
                                        $statusClass = 'bg-success';
                                        $statusText = 'نشط';
                                        break;
                                    case 'pending':
                                        $statusClass = 'bg-warning';
                                        $statusText = 'في انتظار الموافقة';
                                        break;
                                    case 'inactive':
                                        $statusClass = 'bg-secondary';
                                        $statusText = 'معطل';
                                        break;
                                    default:
                                        $statusClass = 'bg-dark';
                                        $statusText = $seller['status'];
                                }
                                ?>
                                <span class="badge <?php echo $statusClass; ?>"><?php echo $statusText; ?></span>
                            </td>
                            <td>
                                <div class="seller-stats">
                                    <small class="d-block"><strong><?php echo $seller['total_orders']; ?></strong> طلب</small>
                                    <small class="d-block text-success"><strong><?php echo $seller['completed_orders']; ?></strong> مكتمل</small>
                                    <small class="d-block text-primary"><strong><?php echo number_format($seller['total_sales'], 2); ?></strong> د.م</small>
                                    <?php if ($seller['total_orders'] > 0): ?>
                                    <small class="text-muted">معدل الإتمام: <?php echo round(($seller['completed_orders'] / $seller['total_orders']) * 100, 1); ?>%</small>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <small><?php echo date('Y-m-d', strtotime($seller['created_at'])); ?></small>
                                <?php if ($seller['updated_at']): ?>
                                <br><small class="text-muted">آخر تحديث: <?php echo date('Y-m-d', strtotime($seller['updated_at'])); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <?php if ($seller['status'] === 'pending'): ?>
                                    <button class="btn btn-success"
                                            onclick="performAction('approve', <?php echo $seller['id']; ?>, '<?php echo htmlspecialchars($seller['full_name']); ?>')"
                                            title="موافقة">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-danger"
                                            onclick="performAction('reject', <?php echo $seller['id']; ?>, '<?php echo htmlspecialchars($seller['full_name']); ?>')"
                                            title="رفض">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <?php elseif ($seller['status'] === 'active'): ?>
                                    <button class="btn btn-warning"
                                            onclick="performAction('deactivate', <?php echo $seller['id']; ?>, '<?php echo htmlspecialchars($seller['full_name']); ?>')"
                                            title="تعطيل">
                                        <i class="fas fa-pause"></i>
                                    </button>
                                    <?php elseif ($seller['status'] === 'inactive'): ?>
                                    <button class="btn btn-success"
                                            onclick="performAction('activate', <?php echo $seller['id']; ?>, '<?php echo htmlspecialchars($seller['full_name']); ?>')"
                                            title="تفعيل">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <?php endif; ?>

                                    <button class="btn btn-info"
                                            onclick="viewUser(<?php echo $seller['id']; ?>)"
                                            title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>

                                    <?php if ($seller['total_orders'] == 0): ?>
                                    <button class="btn btn-danger"
                                            onclick="performAction('delete', <?php echo $seller['id']; ?>, '<?php echo htmlspecialchars($seller['full_name']); ?>')"
                                            title="حذف نهائي">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    <!-- السابق -->
                    <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                        <?php if ($page > 1): ?>
                        <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $statusFilter ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo $searchTerm ? '&search=' . urlencode($searchTerm) : ''; ?>">السابق</a>
                        <?php else: ?>
                        <span class="page-link">السابق</span>
                        <?php endif; ?>
                    </li>

                    <!-- أرقام الصفحات -->
                    <?php
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);

                    if ($startPage > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=1<?php echo $statusFilter ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo $searchTerm ? '&search=' . urlencode($searchTerm) : ''; ?>">1</a>
                        </li>
                        <?php if ($startPage > 2): ?>
                        <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo $statusFilter ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo $searchTerm ? '&search=' . urlencode($searchTerm) : ''; ?>"><?php echo $i; ?></a>
                    </li>
                    <?php endfor; ?>

                    <?php if ($endPage < $totalPages): ?>
                        <?php if ($endPage < $totalPages - 1): ?>
                        <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo $statusFilter ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo $searchTerm ? '&search=' . urlencode($searchTerm) : ''; ?>"><?php echo $totalPages; ?></a>
                        </li>
                    <?php endif; ?>

                    <!-- التالي -->
                    <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                        <?php if ($page < $totalPages): ?>
                        <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $statusFilter ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo $searchTerm ? '&search=' . urlencode($searchTerm) : ''; ?>">التالي</a>
                        <?php else: ?>
                        <span class="page-link">التالي</span>
                        <?php endif; ?>
                    </li>
                </ul>

                <!-- معلومات الصفحة -->
                <div class="text-center mt-3">
                    <small class="text-muted">
                        عرض <?php echo (($page - 1) * $limit) + 1; ?> إلى <?php echo min($page * $limit, $totalSellers); ?> من أصل <?php echo $totalSellers; ?> بائع
                    </small>
                </div>
            </nav>
            <?php endif; ?>
        </div>

<?php
// تضمين footer المدير
include 'includes/admin_footer.php';
?>
