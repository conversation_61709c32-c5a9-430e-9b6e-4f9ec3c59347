<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isUserLoggedIn()) {
    redirect('../pages/login.php');
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect('../pages/dashboard.php');
}

$pageTitle = 'إدارة المنتجات';

// الحصول على الإحصائيات
$stats = getAdminStats();

$message = '';
$messageType = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        // إضافة منتج جديد
        $name = sanitizeInput($_POST['name'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');
        $category = sanitizeInput($_POST['category'] ?? '');
        $supplier_name = sanitizeInput($_POST['supplier_name'] ?? '');
        $supplier_price = (float)($_POST['supplier_price'] ?? 0);
        $suggested_price = (float)($_POST['suggested_price'] ?? 0);
        $image_url = sanitizeInput($_POST['image_url'] ?? '');
        $stock_status = $_POST['stock_status'] ?? 'in_stock';
        
        if ($name && $supplier_price > 0 && $suggested_price > 0) {
            try {
                $profit_margin = calculateProfitMargin($suggested_price, $supplier_price);
                
                $stmt = $db->prepare("
                    INSERT INTO products (name, description, category, supplier_name, supplier_price, suggested_price, profit_margin, image_url, stock_status, status, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', NOW())
                ");
                
                $stmt->execute([
                    $name, $description, $category, $supplier_name, 
                    $supplier_price, $suggested_price, $profit_margin, 
                    $image_url, $stock_status
                ]);
                
                $message = 'تم إضافة المنتج بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في إضافة المنتج';
                $messageType = 'error';
            }
        } else {
            $message = 'يرجى ملء جميع الحقول المطلوبة';
            $messageType = 'error';
        }
    } elseif ($action === 'toggle_status') {
        $productId = (int)($_POST['product_id'] ?? 0);
        $newStatus = $_POST['new_status'] ?? '';
        
        if ($productId > 0 && in_array($newStatus, ['active', 'inactive'])) {
            try {
                $stmt = $db->prepare("UPDATE products SET status = ? WHERE id = ?");
                $stmt->execute([$newStatus, $productId]);
                $message = 'تم تحديث حالة المنتج بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تحديث حالة المنتج';
                $messageType = 'error';
            }
        }
    } elseif ($action === 'delete') {
        $productId = (int)($_POST['product_id'] ?? 0);
        
        if ($productId > 0) {
            try {
                $stmt = $db->prepare("DELETE FROM products WHERE id = ?");
                $stmt->execute([$productId]);
                $message = 'تم حذف المنتج بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في حذف المنتج';
                $messageType = 'error';
            }
        }
    }
}

// فلترة المنتجات
$statusFilter = $_GET['status'] ?? '';
$categoryFilter = $_GET['category'] ?? '';
$searchQuery = $_GET['search'] ?? '';

$whereConditions = [];
$params = [];

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

if ($categoryFilter) {
    $whereConditions[] = "category = ?";
    $params[] = $categoryFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(name LIKE ? OR description LIKE ? OR supplier_name LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// جلب المنتجات
try {
    $stmt = $db->prepare("SELECT * FROM products $whereClause ORDER BY created_at DESC");
    $stmt->execute($params);
    $products = $stmt->fetchAll();
} catch (PDOException $e) {
    $products = [];
}

// جلب الفئات المتاحة
try {
    $stmt = $db->prepare("SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' ORDER BY category");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    $categories = [];
}

// CSS إضافي خاص بهذه الصفحة
$additional_css = '
/* تخصيصات إضافية خاصة بصفحة إدارة المنتجات */
.product-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.product-image {
    height: 200px;
    object-fit: cover;
}

.filter-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
}

.btn-group .btn {
    margin: 2px;
}
';

// JavaScript إضافي خاص بهذه الصفحة
$additional_js = '
function editProduct(product) {
    alert("سيتم إضافة نموذج التعديل قريباً");
}

// حساب هامش الربح تلقائياً
const supplierPriceInput = document.getElementById("supplier_price");
const suggestedPriceInput = document.getElementById("suggested_price");

if (supplierPriceInput && suggestedPriceInput) {
    supplierPriceInput.addEventListener("input", calculateProfitMargin);
    suggestedPriceInput.addEventListener("input", calculateProfitMargin);
}

function calculateProfitMargin() {
    const supplierPrice = parseFloat(document.getElementById("supplier_price").value) || 0;
    const suggestedPrice = parseFloat(document.getElementById("suggested_price").value) || 0;

    if (supplierPrice > 0 && suggestedPrice > 0) {
        const margin = ((suggestedPrice - supplierPrice) / supplierPrice) * 100;
        console.log("هامش الربح: " + margin.toFixed(1) + "%");
    }
}
';

// تضمين header المدير
include 'includes/admin_header.php';
?>

        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="page-title">إدارة المنتجات</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
            </button>
        </div>

        <?php if ($message): ?>
            <?php echo showMessage($message, $messageType); ?>
        <?php endif; ?>

        <!-- Filters -->
        <div class="filter-card">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars($searchQuery); ?>" 
                           placeholder="البحث بالاسم أو الوصف">
                </div>
                <div class="col-md-2">
                    <label for="status" class="form-label">الحالة</label>
                    <select class="form-control" id="status" name="status">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo $statusFilter === 'active' ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo $statusFilter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="category" class="form-label">الفئة</label>
                    <select class="form-control" id="category" name="category">
                        <option value="">جميع الفئات</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo htmlspecialchars($category); ?>" 
                                    <?php echo $categoryFilter === $category ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-1"></i>بحث
                        </button>
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <a href="products.php" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>مسح الفلاتر
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Products Grid -->
        <div class="row">
            <?php if (!empty($products)): ?>
                <?php foreach ($products as $product): ?>
                    <div class="col-md-4 mb-4">
                        <div class="card product-card h-100">
                            <img src="<?php echo htmlspecialchars($product['image_url']); ?>" 
                                 class="card-img-top product-image" 
                                 alt="<?php echo htmlspecialchars($product['name']); ?>">
                            <div class="card-body">
                                <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                <p class="card-text text-muted">
                                    <?php echo htmlspecialchars(substr($product['description'], 0, 100)) . '...'; ?>
                                </p>
                                <div class="mb-2">
                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($product['category']); ?></span>
                                    <span class="badge bg-<?php echo $product['status'] === 'active' ? 'success' : 'danger'; ?>">
                                        <?php echo $product['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                    </span>
                                    <span class="badge bg-info"><?php echo getStockStatusText($product['stock_status']); ?></span>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <small class="text-muted">سعر المورد:</small><br>
                                        <span class="h6 text-primary"><?php echo formatPrice($product['supplier_price']); ?></span>
                                    </div>
                                    <div>
                                        <small class="text-muted">السعر المقترح:</small><br>
                                        <span class="h6 text-success"><?php echo formatPrice($product['suggested_price']); ?></span>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <small class="text-muted">هامش الربح: <?php echo number_format($product['profit_margin'], 1); ?>%</small><br>
                                    <small class="text-muted">المورد: <?php echo htmlspecialchars($product['supplier_name']); ?></small>
                                </div>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100" role="group">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                        <input type="hidden" name="new_status" value="<?php echo $product['status'] === 'active' ? 'inactive' : 'active'; ?>">
                                        <button type="submit" class="btn btn-sm btn-<?php echo $product['status'] === 'active' ? 'warning' : 'success'; ?>">
                                            <i class="fas fa-<?php echo $product['status'] === 'active' ? 'pause' : 'play'; ?>"></i>
                                            <?php echo $product['status'] === 'active' ? 'تعطيل' : 'تفعيل'; ?>
                                        </button>
                                    </form>
                                    
                                    <button type="button" class="btn btn-sm btn-info" 
                                            onclick="editProduct(<?php echo htmlspecialchars(json_encode($product)); ?>)">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                    
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                            <i class="fas fa-trash"></i> حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد منتجات</h5>
                        <p class="text-muted">لم يتم العثور على منتجات بالمعايير المحددة</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                            <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                        </button>
                    </div>
                </div>
            <?php endif; ?>
        </div>

<?php
// تضمين footer المدير
include 'includes/admin_footer.php';
?>
