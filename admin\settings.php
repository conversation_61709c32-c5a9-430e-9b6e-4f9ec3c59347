<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isUserLoggedIn()) {
    redirect('../pages/login.php');
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect('../pages/dashboard.php');
}

$pageTitle = 'إعدادات الموقع';

// الحصول على الإحصائيات
$stats = getAdminStats();

$message = '';
$messageType = '';

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_settings') {
        // هنا يمكن إضافة منطق حفظ الإعدادات في قاعدة البيانات
        // لكن للبساطة سنعرض رسالة نجاح فقط
        $message = 'تم حفظ الإعدادات بنجاح';
        $messageType = 'success';
    }
}

// CSS إضافي خاص بهذه الصفحة
$additional_css = '
/* تخصيصات إضافية خاصة بصفحة الإعدادات */
.settings-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
}

.stats-overview {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.stats-overview .card {
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
}

.form-section {
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.form-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.system-info-table {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
}
';

// JavaScript إضافي خاص بهذه الصفحة
$additional_js = '
// تأثيرات تفاعلية للإعدادات
document.querySelectorAll(".form-control").forEach(input => {
    input.addEventListener("focus", function() {
        this.parentElement.style.transform = "scale(1.02)";
        this.parentElement.style.transition = "all 0.3s ease";
    });
    
    input.addEventListener("blur", function() {
        this.parentElement.style.transform = "scale(1)";
    });
});
';

// تضمين header المدير
include 'includes/admin_header.php';
?>

        <h1 class="page-title">إعدادات الموقع</h1>

        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- إحصائيات سريعة -->
        <div class="stats-overview">
            <h5 class="mb-3"><i class="fas fa-chart-bar me-2"></i>نظرة عامة على النظام</h5>
            <div class="row">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">البائعون</h5>
                            <h3><?php echo $stats['sellers']['total'] ?? 0; ?></h3>
                            <small>
                                نشط: <?php echo $stats['sellers']['active'] ?? 0; ?> | 
                                معلق: <?php echo $stats['sellers']['pending'] ?? 0; ?>
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">المنتجات</h5>
                            <h3><?php echo $stats['products']['total'] ?? 0; ?></h3>
                            <small>نشط: <?php echo $stats['products']['active'] ?? 0; ?></small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">الطلبات</h5>
                            <h3><?php echo $stats['orders']['total'] ?? 0; ?></h3>
                            <small>معلق: <?php echo $stats['orders']['pending'] ?? 0; ?></small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title">الفواتير</h5>
                            <h3><?php echo $stats['invoices']['total'] ?? 0; ?></h3>
                            <small>معلقة: <?php echo $stats['invoices']['pending'] ?? 0; ?></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- إعدادات عامة -->
            <div class="col-md-6">
                <div class="settings-card">
                    <h5 class="mb-4"><i class="fas fa-cog me-2"></i>الإعدادات العامة</h5>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_settings">
                        
                        <div class="form-section">
                            <div class="mb-3">
                                <label class="form-label">اسم الموقع</label>
                                <input type="text" class="form-control" name="site_name" value="منصة الدروب شيبنج">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">وصف الموقع</label>
                                <textarea class="form-control" name="site_description" rows="3">منصة شاملة للدروب شيبنج مع دعم اللغة العربية</textarea>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني للإدارة</label>
                                <input type="email" class="form-control" name="admin_email" value="<EMAIL>">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="text" class="form-control" name="phone" value="+212 6 12 34 56 78">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">العنوان</label>
                                <textarea class="form-control" name="address" rows="2">المملكة المغربية</textarea>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>حفظ الإعدادات
                        </button>
                    </form>
                </div>
            </div>

            <!-- إعدادات العمولة -->
            <div class="col-md-6">
                <div class="settings-card">
                    <h5 class="mb-4"><i class="fas fa-percentage me-2"></i>إعدادات العمولة</h5>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_commission">
                        
                        <div class="form-section">
                            <div class="mb-3">
                                <label class="form-label">معدل العمولة الافتراضي (%)</label>
                                <input type="number" class="form-control" name="default_commission" value="10" step="0.01" min="0" max="100">
                                <small class="text-muted">سيتم تطبيق هذا المعدل على البائعين الجدد</small>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الحد الأدنى (%)</label>
                                        <input type="number" class="form-control" name="min_commission" value="5" step="0.01" min="0" max="100">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الحد الأقصى (%)</label>
                                        <input type="number" class="form-control" name="max_commission" value="30" step="0.01" min="0" max="100">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="auto_calculate_commission" id="autoCommission" checked>
                                    <label class="form-check-label" for="autoCommission">
                                        حساب العمولة تلقائياً عند إتمام الطلب
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات العمولة
                        </button>
                    </form>
                </div>
            </div>

            <!-- إعدادات الطلبات -->
            <div class="col-md-6">
                <div class="settings-card">
                    <h5 class="mb-4"><i class="fas fa-shopping-cart me-2"></i>إعدادات الطلبات</h5>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_orders">
                        
                        <div class="form-section">
                            <div class="mb-3">
                                <label class="form-label">الحد الأدنى لقيمة الطلب</label>
                                <input type="number" class="form-control" name="min_order_amount" value="50" step="0.01" min="0">
                                <small class="text-muted">بالدرهم المغربي</small>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">مدة انتظار تأكيد الطلب (بالساعات)</label>
                                <input type="number" class="form-control" name="order_confirmation_timeout" value="24" min="1">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">مدة انتظار الشحن (بالأيام)</label>
                                <input type="number" class="form-control" name="shipping_timeout" value="7" min="1">
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="auto_cancel_orders" id="autoCancelOrders">
                                    <label class="form-check-label" for="autoCancelOrders">
                                        إلغاء الطلبات المعلقة تلقائياً بعد انتهاء المهلة
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات الطلبات
                        </button>
                    </form>
                </div>
            </div>

            <!-- إعدادات الإشعارات -->
            <div class="col-md-6">
                <div class="settings-card">
                    <h5 class="mb-4"><i class="fas fa-bell me-2"></i>إعدادات الإشعارات</h5>
                    <form method="POST">
                        <input type="hidden" name="action" value="update_notifications">
                        
                        <div class="form-section">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="email_new_orders" id="emailNewOrders" checked>
                                    <label class="form-check-label" for="emailNewOrders">
                                        إرسال إشعار بريد إلكتروني عند وصول طلب جديد
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="email_new_sellers" id="emailNewSellers" checked>
                                    <label class="form-check-label" for="emailNewSellers">
                                        إرسال إشعار عند تسجيل بائع جديد
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="sms_notifications" id="smsNotifications">
                                    <label class="form-check-label" for="smsNotifications">
                                        تفعيل إشعارات الرسائل النصية
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <div class="mb-3">
                                <label class="form-label">بريد إلكتروني للإشعارات</label>
                                <input type="email" class="form-control" name="notification_email" value="<EMAIL>">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>حفظ إعدادات الإشعارات
                        </button>
                    </form>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="col-md-12">
                <div class="settings-card">
                    <h5 class="mb-4"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="system-info-table">
                                <table class="table table-borderless mb-0">
                                    <tr>
                                        <td><strong>إصدار النظام:</strong></td>
                                        <td>1.0.0</td>
                                    </tr>
                                    <tr>
                                        <td><strong>إصدار PHP:</strong></td>
                                        <td><?php echo phpversion(); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>قاعدة البيانات:</strong></td>
                                        <td>MySQL</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الخادم:</strong></td>
                                        <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف'; ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="system-info-table">
                                <table class="table table-borderless mb-0">
                                    <tr>
                                        <td><strong>آخر تحديث:</strong></td>
                                        <td><?php echo date('Y-m-d H:i:s'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>المطور:</strong></td>
                                        <td>فريق التطوير</td>
                                    </tr>
                                    <tr>
                                        <td><strong>الدعم الفني:</strong></td>
                                        <td><EMAIL></td>
                                    </tr>
                                    <tr>
                                        <td><strong>الترخيص:</strong></td>
                                        <td>مرخص للاستخدام التجاري</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

<?php
// تضمين footer المدير
include 'includes/admin_footer.php';
?>
