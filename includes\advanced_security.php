<?php
/**
 * ملف الحماية المتقدمة
 * يحتوي على دوال الحماية المتقدمة ضد جميع أنواع الهجمات
 */

// منع الوصول المباشر للملف
if (!defined('SECURITY_CHECK')) {
    die('Access Denied');
}

/**
 * فحص شامل للأمان عند بداية كل طلب
 */
function performSecurityCheck() {
    // فحص IP المحظورة
    if (!checkBlockedIP()) {
        http_response_code(403);
        die('Access Denied - IP Blocked');
    }
    
    // فحص User Agent المشبوه
    if (!checkUserAgent()) {
        logSecurityEvent('suspicious_user_agent', [
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ], 'medium');
        
        // حظر IP لمدة ساعة
        blockIP($_SERVER['REMOTE_ADDR'] ?? '', 'Suspicious User Agent', 3600);
        http_response_code(403);
        die('Access Denied - Suspicious Activity');
    }
    
    // فحص معدل الطلبات
    if (!checkRequestRate()) {
        logSecurityEvent('rate_limit_exceeded', [
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'requests_per_minute' => getRateLimit($_SERVER['REMOTE_ADDR'] ?? '')
        ], 'high');
        
        http_response_code(429);
        die('Too Many Requests');
    }
    
    // فحص الجلسة
    if (session_status() === PHP_SESSION_ACTIVE) {
        if (!validateSessionFingerprint() || !checkSessionTimeout()) {
            session_destroy();
            if (isset($_SESSION['user_id'])) {
                logSecurityEvent('session_hijack_attempt', [
                    'user_id' => $_SESSION['user_id']
                ], 'critical');
            }
        }
    }
    
    // فحص الطلبات المشبوهة
    checkSuspiciousRequests();
}

/**
 * فحص User Agent المشبوه
 */
function checkUserAgent() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // قائمة User Agents المحظورة
    $blockedAgents = [
        'bot', 'crawler', 'spider', 'scraper', 'scanner',
        'curl', 'wget', 'python', 'perl', 'java',
        'sqlmap', 'nikto', 'nmap', 'masscan',
        'havij', 'acunetix', 'nessus', 'openvas'
    ];
    
    $userAgentLower = strtolower($userAgent);
    
    foreach ($blockedAgents as $blocked) {
        if (strpos($userAgentLower, $blocked) !== false) {
            return false;
        }
    }
    
    // فحص User Agent فارغ أو قصير جداً
    if (empty($userAgent) || strlen($userAgent) < 10) {
        return false;
    }
    
    return true;
}

/**
 * فحص معدل الطلبات
 */
function checkRequestRate($maxRequests = 60, $timeWindow = 60) {
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';
    $cacheKey = 'rate_limit_' . md5($ipAddress);
    
    if (function_exists('apcu_fetch')) {
        $requests = apcu_fetch($cacheKey);
        if ($requests === false) {
            $requests = [];
        }
    } else {
        if (!isset($_SESSION[$cacheKey])) {
            $_SESSION[$cacheKey] = [];
        }
        $requests = $_SESSION[$cacheKey];
    }
    
    $currentTime = time();
    
    // تنظيف الطلبات القديمة
    $requests = array_filter($requests, function($timestamp) use ($currentTime, $timeWindow) {
        return ($currentTime - $timestamp) < $timeWindow;
    });
    
    // إضافة الطلب الحالي
    $requests[] = $currentTime;
    
    // حفظ البيانات
    if (function_exists('apcu_store')) {
        apcu_store($cacheKey, $requests, $timeWindow);
    } else {
        $_SESSION[$cacheKey] = $requests;
    }
    
    return count($requests) <= $maxRequests;
}

/**
 * الحصول على معدل الطلبات الحالي
 */
function getRateLimit($ipAddress) {
    $cacheKey = 'rate_limit_' . md5($ipAddress);
    
    if (function_exists('apcu_fetch')) {
        $requests = apcu_fetch($cacheKey);
        return $requests ? count($requests) : 0;
    } else {
        return isset($_SESSION[$cacheKey]) ? count($_SESSION[$cacheKey]) : 0;
    }
}

/**
 * فحص الطلبات المشبوهة
 */
function checkSuspiciousRequests() {
    $suspiciousPatterns = [
        // SQL Injection patterns
        '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)/i',
        '/(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/i',
        '/\'\s*(OR|AND)\s*\'/i',
        
        // XSS patterns
        '/<script[^>]*>.*?<\/script>/i',
        '/javascript\s*:/i',
        '/on\w+\s*=/i',
        
        // Path traversal patterns
        '/\.\.\//i',
        '/\.\.\\\\/i',
        
        // Command injection patterns
        '/;\s*(cat|ls|pwd|whoami|id|uname)/i',
        '/\|\s*(cat|ls|pwd|whoami|id|uname)/i',
        
        // File inclusion patterns
        '/php:\/\//i',
        '/data:\/\//i',
        '/file:\/\//i'
    ];
    
    $requestData = array_merge($_GET, $_POST, $_COOKIE);
    $requestString = serialize($requestData) . $_SERVER['REQUEST_URI'] ?? '';
    
    foreach ($suspiciousPatterns as $pattern) {
        if (preg_match($pattern, $requestString)) {
            logSecurityEvent('suspicious_request_detected', [
                'pattern' => $pattern,
                'request_data' => $requestData,
                'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
                'referer' => $_SERVER['HTTP_REFERER'] ?? ''
            ], 'critical');
            
            // حظر IP لمدة 24 ساعة
            blockIP($_SERVER['REMOTE_ADDR'] ?? '', 'Suspicious Request Pattern Detected', 86400);
            
            http_response_code(403);
            die('Access Denied - Suspicious Request');
        }
    }
}

/**
 * حماية من هجمات LDAP Injection
 */
function sanitizeLDAP($input) {
    $metaChars = ['\\', '*', '(', ')', "\x00", '/'];
    $quotedMetaChars = [];
    
    foreach ($metaChars as $metaChar) {
        $quotedMetaChars[] = '\\' . dechex(ord($metaChar));
    }
    
    return str_replace($metaChars, $quotedMetaChars, $input);
}

/**
 * حماية من هجمات XML External Entity (XXE)
 */
function sanitizeXML($xmlString) {
    // تعطيل external entities
    libxml_disable_entity_loader(true);
    
    // إزالة DOCTYPE declarations
    $xmlString = preg_replace('/<!DOCTYPE[^>]*>/i', '', $xmlString);
    
    // إزالة ENTITY declarations
    $xmlString = preg_replace('/<!ENTITY[^>]*>/i', '', $xmlString);
    
    return $xmlString;
}

/**
 * حماية من هجمات NoSQL Injection
 */
function sanitizeNoSQL($input) {
    if (is_array($input)) {
        return array_map('sanitizeNoSQL', $input);
    }
    
    // إزالة العمليات الخطيرة في MongoDB
    $dangerousOperators = ['$where', '$regex', '$ne', '$gt', '$lt', '$gte', '$lte', '$in', '$nin'];
    
    if (is_string($input)) {
        foreach ($dangerousOperators as $operator) {
            $input = str_replace($operator, '', $input);
        }
    }
    
    return $input;
}

/**
 * حماية من هجمات Server-Side Request Forgery (SSRF)
 */
function validateURL($url) {
    // التحقق من صحة URL
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }
    
    $parsedUrl = parse_url($url);
    
    // منع الوصول للشبكات المحلية
    $blockedHosts = [
        'localhost', '127.0.0.1', '0.0.0.0',
        '10.', '172.16.', '172.17.', '172.18.', '172.19.',
        '172.20.', '172.21.', '172.22.', '172.23.',
        '172.24.', '172.25.', '172.26.', '172.27.',
        '172.28.', '172.29.', '172.30.', '172.31.',
        '192.168.', '169.254.'
    ];
    
    $host = $parsedUrl['host'] ?? '';
    
    foreach ($blockedHosts as $blockedHost) {
        if (strpos($host, $blockedHost) === 0) {
            return false;
        }
    }
    
    // منع البروتوكولات الخطيرة
    $allowedSchemes = ['http', 'https'];
    $scheme = $parsedUrl['scheme'] ?? '';
    
    if (!in_array($scheme, $allowedSchemes)) {
        return false;
    }
    
    return true;
}

/**
 * حماية من هجمات Clickjacking
 */
function setSecurityHeaders() {
    // منع Clickjacking
    header('X-Frame-Options: DENY');
    
    // منع MIME type sniffing
    header('X-Content-Type-Options: nosniff');
    
    // تفعيل XSS Protection
    header('X-XSS-Protection: 1; mode=block');
    
    // Content Security Policy
    header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self';");
    
    // Strict Transport Security (HTTPS only)
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        header('Strict-Transport-Security: max-age=63072000; includeSubDomains; preload');
    }
    
    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Permissions Policy
    header('Permissions-Policy: geolocation=(), microphone=(), camera=()');
}

/**
 * تشفير البيانات الحساسة
 */
function encryptSensitiveData($data, $key = null) {
    if (!$key) {
        $key = hash('sha256', 'ProGet_Encryption_Key_2024', true);
    }
    
    $iv = random_bytes(16);
    $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
    
    return base64_encode($iv . $encrypted);
}

/**
 * فك تشفير البيانات الحساسة
 */
function decryptSensitiveData($encryptedData, $key = null) {
    if (!$key) {
        $key = hash('sha256', 'ProGet_Encryption_Key_2024', true);
    }
    
    $data = base64_decode($encryptedData);
    $iv = substr($data, 0, 16);
    $encrypted = substr($data, 16);
    
    return openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);
}

// تطبيق الحماية الأساسية عند تحميل الملف
if (!headers_sent()) {
    setSecurityHeaders();
}

// تشغيل فحص الأمان
performSecurityCheck();
?>
