/* لوحة تحكم المدير - ملف CSS منفصل */

:root {
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 70px;
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --sidebar-bg: #2c3e50;
    --sidebar-hover: #34495e;
    --text-light: #ecf0f1;
    --border-color: #34495e;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

/* الشريط العلوي */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: white;
    transform: scale(1.05);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    padding: 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.1);
    transform: scale(1.1);
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.user-profile:hover {
    background: rgba(255,255,255,0.1);
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.8;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.user-avatar:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.user-dropdown-arrow {
    margin-right: 8px;
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.user-profile:hover .user-dropdown-arrow {
    transform: rotate(180deg);
}

/* قائمة المستخدم المنسدلة */
.user-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    width: 280px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-dropdown-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.user-dropdown-info {
    flex: 1;
}

.user-dropdown-name {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 2px;
}

.user-dropdown-role {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 2px;
}

.user-dropdown-email {
    font-size: 0.8rem;
    opacity: 0.8;
}

.user-dropdown-menu {
    padding: 10px 0;
}

.user-dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #2c3e50;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.user-dropdown-item:hover {
    background: #f8f9fa;
    color: var(--primary-color);
    transform: translateX(5px);
}

.user-dropdown-item.logout {
    color: #e74c3c;
}

.user-dropdown-item.logout:hover {
    background: #fdf2f2;
    color: #c0392b;
}

.user-dropdown-divider {
    height: 1px;
    background: #eee;
    margin: 8px 0;
}

/* تحديث notification-icon للروابط */
.notification-icon {
    text-decoration: none;
    color: white;
}

.notification-icon:hover {
    color: white;
    text-decoration: none;
}

/* أيقونات الإشعارات في الشريط العلوي */
.navbar-notifications {
    display: flex;
    align-items: center;
    gap: 15px;
}

.notification-icon {
    position: relative;
    background: rgba(255,255,255,0.1);
    border: none;
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-icon:hover {
    background: rgba(255,255,255,0.2);
    transform: scale(1.1);
    color: white;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
    border: 2px solid white;
}

.notification-badge.zero {
    display: none;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(255, 71, 87, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
    }
}

/* قائمة الإشعارات المنسدلة */
.notifications-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    width: 350px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.notifications-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notifications-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px 15px 0 0;
}

.notifications-header h6 {
    margin: 0;
    font-weight: 600;
}

.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: #f8f9fa;
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item.unread {
    background: #e3f2fd;
    border-right: 4px solid var(--primary-color);
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.notification-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.notification-avatar.order {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.notification-avatar.user {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.notification-avatar.product {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.notification-text {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
    font-size: 0.9rem;
}

.notification-desc {
    color: #7f8c8d;
    font-size: 0.8rem;
    margin-bottom: 4px;
}

.notification-time {
    color: #bdc3c7;
    font-size: 0.75rem;
}

.notifications-footer {
    padding: 15px 20px;
    text-align: center;
    border-top: 1px solid #eee;
}

.notifications-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
}

.notifications-footer a:hover {
    color: var(--secondary-color);
}

/* تأثيرات إضافية */
.notification-icon.has-notifications {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* الشريط الجانبي */
.sidebar {
    position: fixed;
    top: var(--header-height);
    right: 0;
    width: var(--sidebar-width);
    height: calc(100vh - var(--header-height));
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--text-light);
    transition: all 0.3s ease;
    z-index: 999;
    overflow-y: auto;
    overflow-x: hidden;
    box-shadow: -2px 0 15px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
    position: relative;
    z-index: 1;
}

.sidebar-menu li {
    margin: 5px 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: var(--text-light);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
    position: relative;
}

.sidebar-menu a:hover,
.sidebar-menu a.active {
    background: rgba(255, 255, 255, 0.2);
    border-right-color: white;
    color: white;
    transform: translateX(-5px);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.sidebar-menu a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 0;
    background: rgba(255, 255, 255, 0.8);
    transition: width 0.3s ease;
}

.sidebar-menu a:hover::before,
.sidebar-menu a.active::before {
    width: 4px;
}

.sidebar-menu i {
    width: 20px;
    text-align: center;
    margin-left: 15px;
    font-size: 1.1rem;
    z-index: 1;
    position: relative;
}

.sidebar-menu span {
    transition: opacity 0.3s ease;
    z-index: 1;
    position: relative;
}

.sidebar.collapsed .sidebar-menu span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar.collapsed .sidebar-menu a {
    justify-content: center;
    padding: 15px 10px;
}

.sidebar.collapsed .sidebar-menu i {
    margin-left: 0;
}

/* المحتوى الرئيسي */
.main-content {
    margin-top: var(--header-height);
    margin-right: var(--sidebar-width);
    padding: 30px;
    transition: all 0.3s ease;
    min-height: calc(100vh - var(--header-height));
}

.main-content.expanded {
    margin-right: var(--sidebar-collapsed-width);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: none;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.stats-card:hover .stats-icon {
    transform: scale(1.1) rotate(5deg);
}

.stats-icon.users {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.products {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.orders {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.revenue {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
    transition: all 0.3s ease;
}

.stats-card:hover .stats-number {
    transform: scale(1.05);
}

.stats-label {
    color: #7f8c8d;
    font-weight: 500;
    margin-bottom: 10px;
}

.stats-change {
    font-size: 0.85rem;
    margin-top: 10px;
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-block;
}

.stats-change.positive {
    color: var(--success-color);
    background: rgba(39, 174, 96, 0.1);
}

.stats-change.negative {
    color: var(--danger-color);
    background: rgba(231, 76, 60, 0.1);
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
        padding: 20px;
    }
    
    .user-info {
        display: none;
    }
    
    .stats-card {
        margin-bottom: 20px;
    }
}

/* تحسينات إضافية */
.page-title {
    color: #2c3e50;
    font-weight: 700;
    margin-bottom: 30px;
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 2px;
}

.welcome-text {
    color: #7f8c8d;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* شريط التمرير المخصص */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card {
    animation: fadeInUp 0.6s ease forwards;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .top-navbar {
        display: none;
    }
    
    .main-content {
        margin: 0;
        padding: 20px;
    }
}
