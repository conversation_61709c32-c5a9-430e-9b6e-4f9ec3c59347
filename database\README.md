# قاعدة بيانات ProGet - دليل التثبيت

## 📋 نظرة عامة

قاعدة بيانات شاملة لمنصة التجارة الإلكترونية ProGet مع نظام البائعين المتعددين.

## 🗄️ الجداول المتضمنة

### الجداول الأساسية:
- **users** - المستخدمين (مديرين، بائعين، عملاء)
- **categories** - فئات المنتجات
- **products** - المنتجات
- **product_images** - صور المنتجات
- **product_attributes** - خصائص المنتجات

### جداول الطلبات:
- **orders** - الطلبات
- **order_items** - عناصر الطلبات
- **order_status_history** - تاريخ حالة الطلبات

### جداول التفاعل:
- **cart** - سلة التسوق
- **wishlist** - قائمة الأمنيات
- **reviews** - التقييمات والمراجعات

### جداول الخصومات:
- **coupons** - كوبونات الخصم
- **coupon_usage** - استخدام الكوبونات

### جداول النظام:
- **notifications** - الإشعارات
- **settings** - إعدادات النظام
- **activity_logs** - سجل النشاطات

## 🚀 طريقة التثبيت

### الطريقة الأولى: عبر phpMyAdmin
1. افتح phpMyAdmin
2. انقر على "استيراد" (Import)
3. اختر ملف `proget_database.sql`
4. انقر على "تنفيذ" (Go)

### الطريقة الثانية: عبر سطر الأوامر
```bash
mysql -u root -p < proget_database.sql
```

### الطريقة الثالثة: عبر MySQL Workbench
1. افتح MySQL Workbench
2. اتصل بالخادم
3. File > Open SQL Script
4. اختر ملف `proget_database.sql`
5. انقر على Execute

## 👤 حسابات المستخدمين الافتراضية

### 🔑 المدير الرئيسي
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `password`
- **الدور:** admin
- **الحالة:** نشط

### 🏪 البائع التجريبي
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `password`
- **الدور:** seller
- **اسم المتجر:** متجر الإلكترونيات الحديثة
- **الحالة:** نشط

### 🛒 العميل التجريبي
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `password`
- **الدور:** customer
- **الحالة:** نشط

## 📦 البيانات التجريبية المتضمنة

### الفئات:
- الإلكترونيات (مع فئات فرعية)
- الأزياء والموضة (مع فئات فرعية)
- المنزل والحديقة
- الصحة والجمال
- الرياضة واللياقة
- الكتب والتعليم
- الألعاب والترفيه
- السيارات والمركبات

### المنتجات:
- آيفون 15 برو ماكس
- سامسونج جالاكسي S24 الترا
- لابتوب ديل XPS 13
- ماك بوك آير M2
- كاميرا كانون EOS R6

### الطلبات:
- 4 طلبات تجريبية بحالات مختلفة
- طلب مسلم، طلب مشحون، طلب قيد التجهيز، طلب معلق

### الكوبونات:
- WELCOME10 - خصم 10% للعملاء الجدد
- SAVE50 - خصم 50 درهم

## ⚙️ الإعدادات الافتراضية

- **العملة:** درهم مغربي (MAD)
- **رمز العملة:** د.م
- **نسبة العمولة الافتراضية:** 5%
- **أقل مبلغ للطلب:** 50 درهم
- **أعلى مبلغ للطلب:** 10,000 درهم

## 🔧 إعداد الاتصال

تأكد من تحديث ملف `includes/config.php` بمعلومات قاعدة البيانات:

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'proget_db');
define('DB_USER', 'root'); // أو اسم المستخدم الخاص بك
define('DB_PASS', ''); // كلمة مرور قاعدة البيانات
define('DB_CHARSET', 'utf8mb4');
```

## 🔒 الأمان

### كلمات المرور:
- جميع كلمات المرور مشفرة باستخدام bcrypt
- كلمة المرور الافتراضية: `password`
- **يُنصح بتغيير كلمات المرور فوراً بعد التثبيت**

### إنشاء مستخدم قاعدة بيانات منفصل (اختياري):
```sql
CREATE USER 'proget_user'@'localhost' IDENTIFIED BY 'كلمة_مرور_قوية';
GRANT ALL PRIVILEGES ON proget_db.* TO 'proget_user'@'localhost';
FLUSH PRIVILEGES;
```

## 📊 إحصائيات قاعدة البيانات

- **عدد الجداول:** 15 جدول
- **عدد المستخدمين:** 3 (مدير + بائع + عميل)
- **عدد الفئات:** 12 فئة (8 رئيسية + 4 فرعية)
- **عدد المنتجات:** 5 منتجات
- **عدد الطلبات:** 4 طلبات
- **عدد الكوبونات:** 2 كوبون

## 🔍 فهارس قاعدة البيانات

تم إنشاء فهارس محسنة لتحسين الأداء:
- فهارس على الحقول المستخدمة في البحث
- فهارس على المفاتيح الخارجية
- فهارس على التواريخ والحالات
- فهرس نص كامل للبحث في المنتجات

## 🚨 ملاحظات مهمة

1. **النسخ الاحتياطي:** احتفظ بنسخة احتياطية قبل التثبيت
2. **الصلاحيات:** تأكد من وجود صلاحيات كافية لإنشاء قاعدة البيانات
3. **الترميز:** قاعدة البيانات تستخدم UTF8MB4 لدعم الرموز التعبيرية
4. **الحجم:** قاعدة البيانات تحتاج حوالي 5MB مساحة فارغة

## 🔄 التحديثات المستقبلية

لتحديث قاعدة البيانات في المستقبل:
1. احتفظ بنسخة احتياطية
2. قم بتشغيل سكريبت التحديث
3. تحقق من سلامة البيانات

## 📞 الدعم

في حالة مواجهة مشاكل:
1. تحقق من سجلات الأخطاء
2. تأكد من إعدادات قاعدة البيانات
3. راجع صلاحيات المستخدم

---

**تم إنشاء قاعدة البيانات بواسطة:** ProGet Development Team  
**التاريخ:** 2024-06-16  
**الإصدار:** 1.0.0
