<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات البائع
requireSeller();

$pageTitle = 'المنتجات المتاحة';
$currentUser = getCurrentUser();
$message = '';
$messageType = '';

// معالجة إضافة منتج إلى متجر البائع
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_to_store') {
    $productId = (int)($_POST['product_id'] ?? 0);
    $sellingPrice = (float)($_POST['selling_price'] ?? 0);
    
    if ($productId > 0 && $sellingPrice > 0) {
        try {
            // التحقق من وجود المنتج وأنه نشط
            $stmt = $db->prepare("SELECT * FROM products WHERE id = ? AND status = 'active'");
            $stmt->execute([$productId]);
            $product = $stmt->fetch();
            
            if ($product) {
                // التحقق من عدم إضافة المنتج مسبقاً
                $stmt = $db->prepare("SELECT id FROM seller_products WHERE seller_id = ? AND product_id = ?");
                $stmt->execute([$currentUser['id'], $productId]);
                
                if (!$stmt->fetch()) {
                    // إضافة المنتج إلى متجر البائع
                    $stmt = $db->prepare("
                        INSERT INTO seller_products (seller_id, product_id, selling_price, status, added_at) 
                        VALUES (?, ?, ?, 'active', NOW())
                    ");
                    $stmt->execute([$currentUser['id'], $productId, $sellingPrice]);
                    
                    $message = 'تم إضافة المنتج إلى متجرك بنجاح';
                    $messageType = 'success';
                } else {
                    $message = 'هذا المنتج موجود بالفعل في متجرك';
                    $messageType = 'warning';
                }
            } else {
                $message = 'المنتج غير متاح';
                $messageType = 'error';
            }
        } catch (PDOException $e) {
            $message = 'حدث خطأ في إضافة المنتج';
            $messageType = 'error';
        }
    } else {
        $message = 'يرجى إدخال سعر البيع';
        $messageType = 'error';
    }
}

// فلترة المنتجات
$categoryFilter = $_GET['category'] ?? '';
$searchQuery = $_GET['search'] ?? '';

$whereConditions = ["status = 'active'"];
$params = [];

if ($categoryFilter) {
    $whereConditions[] = "category = ?";
    $params[] = $categoryFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(name LIKE ? OR description LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب المنتجات المتاحة
try {
    $stmt = $db->prepare("SELECT * FROM products WHERE $whereClause ORDER BY created_at DESC");
    $stmt->execute($params);
    $products = $stmt->fetchAll();
} catch (PDOException $e) {
    $products = [];
}

// جلب المنتجات المضافة بالفعل من قبل البائع
try {
    $stmt = $db->prepare("SELECT product_id FROM seller_products WHERE seller_id = ?");
    $stmt->execute([$currentUser['id']]);
    $addedProducts = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    $addedProducts = [];
}

// جلب الفئات المتاحة
try {
    $stmt = $db->prepare("SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' AND status = 'active' ORDER BY category");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (PDOException $e) {
    $categories = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="https://via.placeholder.com/80x80?text=<?php echo substr($currentUser['full_name'], 0, 1); ?>" 
                         class="rounded-circle mb-2" alt="Seller">
                    <h6 class="text-white"><?php echo htmlspecialchars($currentUser['full_name']); ?></h6>
                    <small class="text-white-50">بائع</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="products.php">
                            <i class="fas fa-shopping-bag me-2"></i>المنتجات المتاحة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my-products.php">
                            <i class="fas fa-boxes me-2"></i>منتجاتي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">المنتجات المتاحة</h1>
                <a href="my-products.php" class="btn btn-outline-primary">
                    <i class="fas fa-boxes me-2"></i>منتجاتي (<?php echo count($addedProducts); ?>)
                </a>
            </div>

            <?php if ($message): ?>
                <?php echo showMessage($message, $messageType); ?>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="البحث بالاسم أو الوصف">
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">الفئة</label>
                            <select class="form-control" id="category" name="category">
                                <option value="">جميع الفئات</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?php echo htmlspecialchars($category); ?>" 
                                            <?php echo $categoryFilter === $category ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($category); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <a href="products.php" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i>مسح الفلاتر
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="row">
                <?php if (!empty($products)): ?>
                    <?php foreach ($products as $product): ?>
                        <?php $isAdded = in_array($product['id'], $addedProducts); ?>
                        <div class="col-md-4 mb-4">
                            <div class="card product-card h-100 <?php echo $isAdded ? 'border-success' : ''; ?>">
                                <?php if ($isAdded): ?>
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>مضاف
                                        </span>
                                    </div>
                                <?php endif; ?>
                                
                                <img src="<?php echo htmlspecialchars($product['image_url']); ?>" 
                                     class="card-img-top product-image" 
                                     alt="<?php echo htmlspecialchars($product['name']); ?>">
                                <div class="card-body">
                                    <h5 class="card-title"><?php echo htmlspecialchars($product['name']); ?></h5>
                                    <p class="card-text text-muted">
                                        <?php echo htmlspecialchars(substr($product['description'], 0, 100)) . '...'; ?>
                                    </p>
                                    <div class="mb-2">
                                        <span class="badge bg-secondary"><?php echo htmlspecialchars($product['category']); ?></span>
                                        <span class="badge bg-info"><?php echo getStockStatusText($product['stock_status']); ?></span>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <div>
                                            <small class="text-muted">سعر المورد:</small><br>
                                            <span class="h6 text-primary"><?php echo formatPrice($product['supplier_price']); ?></span>
                                        </div>
                                        <div>
                                            <small class="text-muted">السعر المقترح:</small><br>
                                            <span class="h6 text-success"><?php echo formatPrice($product['suggested_price']); ?></span>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            هامش الربح المقترح: <?php echo number_format($product['profit_margin'], 1); ?>%<br>
                                            المورد: <?php echo htmlspecialchars($product['supplier_name']); ?>
                                        </small>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <?php if (!$isAdded): ?>
                                        <form method="POST" class="add-product-form">
                                            <input type="hidden" name="action" value="add_to_store">
                                            <input type="hidden" name="product_id" value="<?php echo $product['id']; ?>">
                                            <div class="mb-2">
                                                <label class="form-label small">سعر البيع الخاص بك:</label>
                                                <input type="number" class="form-control form-control-sm" 
                                                       name="selling_price" step="0.01" min="<?php echo $product['supplier_price']; ?>" 
                                                       value="<?php echo $product['suggested_price']; ?>" required>
                                            </div>
                                            <button type="submit" class="btn btn-primary btn-sm w-100">
                                                <i class="fas fa-plus me-1"></i>إضافة إلى متجري
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <div class="text-center">
                                            <span class="text-success">
                                                <i class="fas fa-check-circle me-1"></i>تم إضافته إلى متجرك
                                            </span>
                                            <br>
                                            <a href="my-products.php" class="btn btn-outline-primary btn-sm mt-2">
                                                إدارة منتجاتي
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد منتجات متاحة</h5>
                            <p class="text-muted">لم يتم العثور على منتجات بالمعايير المحددة</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</div>

<script>
// تحسين تجربة المستخدم عند إضافة المنتجات
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('.add-product-form');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const button = form.querySelector('button[type="submit"]');
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الإضافة...';
        });
    });
});

// حساب الربح المتوقع عند تغيير السعر
function calculateProfit(input, supplierPrice) {
    const sellingPrice = parseFloat(input.value) || 0;
    const profit = sellingPrice - supplierPrice;
    const margin = supplierPrice > 0 ? (profit / supplierPrice) * 100 : 0;
    
    // يمكن إضافة عرض الربح المحسوب هنا
    console.log('الربح المتوقع: ' + profit.toFixed(2) + ' ر.س (' + margin.toFixed(1) + '%)');
}
</script>

<?php include '../includes/footer.php'; ?>
