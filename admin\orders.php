<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isUserLoggedIn()) {
    redirect('../pages/login.php');
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect('../pages/dashboard.php');
}

$pageTitle = 'إدارة الطلبات';

// الحصول على الإحصائيات
$stats = getAdminStats();

$message = '';
$messageType = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $orderId = (int)($_POST['order_id'] ?? 0);
    
    if ($action === 'update_status' && $orderId > 0) {
        $newStatus = $_POST['new_status'] ?? '';
        $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
        
        if (in_array($newStatus, $validStatuses)) {
            try {
                $stmt = $db->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$newStatus, $orderId]);
                $message = 'تم تحديث حالة الطلب بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تحديث حالة الطلب';
                $messageType = 'error';
            }
        }
    }
}

// فلترة الطلبات
$statusFilter = $_GET['status'] ?? '';
$sellerFilter = $_GET['seller'] ?? '';
$searchQuery = $_GET['search'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

$whereConditions = [];
$params = [];

if ($statusFilter) {
    $whereConditions[] = "o.status = ?";
    $params[] = $statusFilter;
}

if ($sellerFilter) {
    $whereConditions[] = "o.seller_id = ?";
    $params[] = $sellerFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(o.order_number LIKE ? OR o.customer_name LIKE ? OR o.customer_email LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(o.created_at) >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(o.created_at) <= ?";
    $params[] = $dateTo;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// جلب الطلبات
try {
    $stmt = $db->prepare("
        SELECT o.*, u.full_name as seller_name, u.username as seller_username,
               COUNT(oi.id) as items_count,
               GROUP_CONCAT(p.name SEPARATOR ', ') as product_names
        FROM orders o 
        JOIN users u ON o.seller_id = u.id 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        $whereClause 
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ");
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
} catch (PDOException $e) {
    $orders = [];
}

// جلب قائمة البائعين للفلترة
try {
    $stmt = $db->prepare("SELECT id, full_name, username FROM users WHERE role = 'seller' ORDER BY full_name");
    $stmt->execute();
    $sellers = $stmt->fetchAll();
} catch (PDOException $e) {
    $sellers = [];
}

// CSS إضافي خاص بهذه الصفحة
$additional_css = '
/* تخصيصات إضافية خاصة بصفحة إدارة الطلبات */
.orders-table {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.filter-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
}

.order-actions .btn {
    margin: 2px;
}

.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}
';

// JavaScript إضافي خاص بهذه الصفحة
$additional_js = '
function viewOrderDetails(orderId) {
    window.open("order-details.php?id=" + orderId, "_blank", "width=800,height=600");
}

function exportOrders() {
    const params = new URLSearchParams(window.location.search);
    params.set("export", "csv");
    window.location.href = "export-orders.php?" + params.toString();
}
';

// تضمين header المدير
include 'includes/admin_header.php';
?>

        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="page-title">إدارة الطلبات</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportOrders()">
                        <i class="fas fa-download me-1"></i>تصدير
                    </button>
                </div>
            </div>
        </div>

            <?php if ($message): ?>
                <?php echo showMessage($message, $messageType); ?>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="رقم الطلب أو العميل">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                <option value="confirmed" <?php echo $statusFilter === 'confirmed' ? 'selected' : ''; ?>>مؤكد</option>
                                <option value="processing" <?php echo $statusFilter === 'processing' ? 'selected' : ''; ?>>قيد المعالجة</option>
                                <option value="shipped" <?php echo $statusFilter === 'shipped' ? 'selected' : ''; ?>>تم الشحن</option>
                                <option value="delivered" <?php echo $statusFilter === 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                                <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="seller" class="form-label">البائع</label>
                            <select class="form-control" id="seller" name="seller">
                                <option value="">جميع البائعين</option>
                                <?php foreach ($sellers as $seller): ?>
                                    <option value="<?php echo $seller['id']; ?>" 
                                            <?php echo $sellerFilter == $seller['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($seller['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($dateFrom); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($dateTo); ?>">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="orders.php" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-times me-1"></i>مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-shopping-cart me-2"></i>قائمة الطلبات (<?php echo count($orders); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($orders)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الطلب</th>
                                        <th>العميل</th>
                                        <th>البائع</th>
                                        <th>المنتجات</th>
                                        <th>المبلغ</th>
                                        <th>الحالة</th>
                                        <th>التاريخ</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($orders as $order): ?>
                                        <tr>
                                            <td>
                                                <strong>#<?php echo htmlspecialchars($order['order_number']); ?></strong>
                                                <?php if ($order['tracking_number']): ?>
                                                    <br><small class="text-muted">تتبع: <?php echo htmlspecialchars($order['tracking_number']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($order['customer_name']); ?></strong>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($order['customer_email']); ?></small>
                                                <?php if ($order['customer_phone']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($order['customer_phone']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($order['seller_name']); ?></strong>
                                                <br><small class="text-muted">@<?php echo htmlspecialchars($order['seller_username']); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?php echo $order['items_count']; ?> منتج</span>
                                                <?php if ($order['product_names']): ?>
                                                    <br><small class="text-muted" title="<?php echo htmlspecialchars($order['product_names']); ?>">
                                                        <?php echo htmlspecialchars(substr($order['product_names'], 0, 30)) . '...'; ?>
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo formatPrice($order['total_amount']); ?></strong>
                                                <br><small class="text-muted">عمولة: <?php echo formatPrice($order['commission_amount']); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $order['status'] === 'delivered' ? 'success' : 
                                                        ($order['status'] === 'pending' ? 'warning' : 
                                                        ($order['status'] === 'cancelled' ? 'danger' : 'info')); 
                                                ?>">
                                                    <?php echo getOrderStatusText($order['status']); ?>
                                                </span>
                                                <br><small class="text-muted">دفع: <?php echo getPaymentStatusText($order['payment_status']); ?></small>
                                            </td>
                                            <td>
                                                <?php echo formatDate($order['created_at']); ?>
                                                <?php if ($order['updated_at'] !== $order['created_at']): ?>
                                                    <br><small class="text-muted">محدث: <?php echo formatDate($order['updated_at']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewOrderDetails(<?php echo $order['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                                                data-bs-toggle="dropdown">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <?php 
                                                            $statusOptions = [
                                                                'pending' => 'في الانتظار',
                                                                'confirmed' => 'مؤكد', 
                                                                'processing' => 'قيد المعالجة',
                                                                'shipped' => 'تم الشحن',
                                                                'delivered' => 'تم التسليم',
                                                                'cancelled' => 'ملغي'
                                                            ];
                                                            foreach ($statusOptions as $status => $label):
                                                                if ($status !== $order['status']):
                                                            ?>
                                                                <li>
                                                                    <form method="POST" style="display: inline;">
                                                                        <input type="hidden" name="action" value="update_status">
                                                                        <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                                                        <input type="hidden" name="new_status" value="<?php echo $status; ?>">
                                                                        <button type="submit" class="dropdown-item">
                                                                            <?php echo $label; ?>
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            <?php 
                                                                endif;
                                                            endforeach; 
                                                            ?>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد طلبات</h5>
                            <p class="text-muted">لم يتم العثور على طلبات بالمعايير المحددة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

<?php
// تضمين footer المدير
include 'includes/admin_footer.php';
?>
