# حماية مجلد الإدارة
# منع الوصول المباشر إلا للمدراء المسجلين

RewriteEngine On

# إعادة توجيه المستخدمين غير المسجلين إلى صفحة تسجيل الدخول
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ ../pages/login.php [R=302,L]

# منع الوصول إلى ملفات النسخ الاحتياطي والملفات الحساسة
<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

# تعطيل عرض محتويات المجلد
Options -Indexes

# حماية إضافية للملفات الحساسة
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>
