<?php
/**
 * نظام CSRF بسيط جداً وفعال
 * يعمل بدون تعقيدات
 */

// منع الوصول المباشر للملف
if (!defined('SECURITY_CHECK')) {
    die('Access Denied');
}

/**
 * توليد CSRF Token بسيط
 */
function getCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * التحقق من CSRF Token
 */
function checkCSRFToken($token) {
    if (!isset($_SESSION['csrf_token'])) {
        return false;
    }
    
    if (empty($token)) {
        return false;
    }
    
    return $_SESSION['csrf_token'] === $token;
}

/**
 * إنشاء حقل CSRF للنموذج
 */
function csrfField() {
    $token = getCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token, ENT_QUOTES, 'UTF-8') . '">';
}

/**
 * التحقق من CSRF في النموذج
 */
function validateCSRF() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        return checkCSRFToken($token);
    }
    return true;
}

/**
 * إعادة تعيين CSRF Token
 */
function renewCSRFToken() {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

/**
 * حذف CSRF Token
 */
function clearCSRFToken() {
    unset($_SESSION['csrf_token']);
}
?>
