<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
requireAdmin();

$pageTitle = 'تفاصيل الطلب';
$currentUser = getCurrentUser();
$orderId = (int)($_GET['id'] ?? 0);

if (!$orderId) {
    echo '<script>window.close();</script>';
    exit;
}

// جلب بيانات الطلب
try {
    $stmt = $db->prepare("
        SELECT o.*, u.full_name as seller_name, u.username as seller_username, u.email as seller_email
        FROM orders o 
        JOIN users u ON o.seller_id = u.id 
        WHERE o.id = ?
    ");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch();
    
    if (!$order) {
        echo '<script>window.close();</script>';
        exit;
    }
} catch (PDOException $e) {
    echo '<script>window.close();</script>';
    exit;
}

// جلب عناصر الطلب
try {
    $stmt = $db->prepare("
        SELECT oi.*, p.name as product_name, p.image_url, p.category
        FROM order_items oi 
        JOIN products p ON oi.product_id = p.id 
        WHERE oi.order_id = ?
    ");
    $stmt->execute([$orderId]);
    $orderItems = $stmt->fetchAll();
} catch (PDOException $e) {
    $orderItems = [];
}

include '../includes/header.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلب #<?php echo htmlspecialchars($order['order_number']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .print-btn { position: fixed; top: 20px; right: 20px; z-index: 1000; }
        @media print {
            .print-btn, .no-print { display: none !important; }
            .container { max-width: 100% !important; }
        }
    </style>
</head>
<body>
    <button class="btn btn-primary print-btn no-print" onclick="window.print()">
        <i class="fas fa-print me-2"></i>طباعة
    </button>

    <div class="container my-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h2><i class="fas fa-shopping-cart me-2"></i>تفاصيل الطلب</h2>
                <h4 class="text-primary">#<?php echo htmlspecialchars($order['order_number']); ?></h4>
            </div>
            <div class="col-md-6 text-end">
                <span class="badge bg-<?php 
                    echo $order['status'] === 'delivered' ? 'success' : 
                        ($order['status'] === 'pending' ? 'warning' : 
                        ($order['status'] === 'cancelled' ? 'danger' : 'info')); 
                ?> fs-6">
                    <?php echo getOrderStatusText($order['status']); ?>
                </span>
                <br>
                <small class="text-muted">تاريخ الطلب: <?php echo formatDate($order['created_at']); ?></small>
            </div>
        </div>

        <div class="row">
            <!-- معلومات العميل -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-user me-2"></i>معلومات العميل</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الاسم:</strong> <?php echo htmlspecialchars($order['customer_name']); ?></p>
                        <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($order['customer_email']); ?></p>
                        <?php if ($order['customer_phone']): ?>
                            <p><strong>الهاتف:</strong> <?php echo htmlspecialchars($order['customer_phone']); ?></p>
                        <?php endif; ?>
                        <p><strong>عنوان الشحن:</strong></p>
                        <div class="bg-light p-2 rounded">
                            <?php echo nl2br(htmlspecialchars($order['shipping_address'])); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- معلومات البائع -->
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-store me-2"></i>معلومات البائع</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>الاسم:</strong> <?php echo htmlspecialchars($order['seller_name']); ?></p>
                        <p><strong>اسم المستخدم:</strong> @<?php echo htmlspecialchars($order['seller_username']); ?></p>
                        <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($order['seller_email']); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الطلب -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>معلومات الطلب</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <p><strong>حالة الطلب:</strong></p>
                                <span class="badge bg-<?php 
                                    echo $order['status'] === 'delivered' ? 'success' : 
                                        ($order['status'] === 'pending' ? 'warning' : 
                                        ($order['status'] === 'cancelled' ? 'danger' : 'info')); 
                                ?>">
                                    <?php echo getOrderStatusText($order['status']); ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <p><strong>حالة الدفع:</strong></p>
                                <span class="badge bg-<?php echo $order['payment_status'] === 'paid' ? 'success' : 'warning'; ?>">
                                    <?php echo getPaymentStatusText($order['payment_status']); ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <?php if ($order['payment_method']): ?>
                                    <p><strong>طريقة الدفع:</strong></p>
                                    <p><?php echo htmlspecialchars($order['payment_method']); ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-3">
                                <?php if ($order['tracking_number']): ?>
                                    <p><strong>رقم التتبع:</strong></p>
                                    <p><?php echo htmlspecialchars($order['tracking_number']); ?></p>
                                    <?php if ($order['shipping_company']): ?>
                                        <small class="text-muted">شركة الشحن: <?php echo htmlspecialchars($order['shipping_company']); ?></small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($order['notes']): ?>
                            <hr>
                            <p><strong>ملاحظات:</strong></p>
                            <div class="bg-light p-2 rounded">
                                <?php echo nl2br(htmlspecialchars($order['notes'])); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- عناصر الطلب -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-boxes me-2"></i>عناصر الطلب</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($orderItems)): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الكمية</th>
                                            <th>سعر الوحدة</th>
                                            <th>الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php 
                                        $subtotal = 0;
                                        foreach ($orderItems as $item): 
                                            $itemTotal = $item['quantity'] * $item['unit_price'];
                                            $subtotal += $itemTotal;
                                        ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <?php if ($item['image_url']): ?>
                                                            <img src="<?php echo htmlspecialchars($item['image_url']); ?>" 
                                                                 alt="<?php echo htmlspecialchars($item['product_name']); ?>" 
                                                                 class="me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                                        <?php endif; ?>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($item['product_name']); ?></strong>
                                                            <?php if ($item['category']): ?>
                                                                <br><small class="text-muted"><?php echo htmlspecialchars($item['category']); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo $item['quantity']; ?></td>
                                                <td><?php echo formatPrice($item['unit_price']); ?></td>
                                                <td><?php echo formatPrice($itemTotal); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3" class="text-end">المجموع الفرعي:</th>
                                            <th><?php echo formatPrice($subtotal); ?></th>
                                        </tr>
                                        <tr>
                                            <th colspan="3" class="text-end">العمولة:</th>
                                            <th><?php echo formatPrice($order['commission_amount']); ?></th>
                                        </tr>
                                        <tr class="table-primary">
                                            <th colspan="3" class="text-end">المجموع الإجمالي:</th>
                                            <th><?php echo formatPrice($order['total_amount']); ?></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        <?php else: ?>
                            <p class="text-muted text-center">لا توجد عناصر في هذا الطلب</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="row no-print">
            <div class="col-12 text-center">
                <button type="button" class="btn btn-secondary" onclick="window.close()">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
                <button type="button" class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
