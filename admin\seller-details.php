<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
requireAdmin();

$pageTitle = 'تفاصيل البائع';
$currentUser = getCurrentUser();
$sellerId = (int)($_GET['id'] ?? 0);

if (!$sellerId) {
    redirect('sellers.php');
}

// جلب بيانات البائع
try {
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ? AND role = 'seller'");
    $stmt->execute([$sellerId]);
    $seller = $stmt->fetch();
    
    if (!$seller) {
        redirect('sellers.php');
    }
} catch (PDOException $e) {
    redirect('sellers.php');
}

// جلب إحصائيات البائع
$stats = getSellerStats($sellerId);

// جلب منتجات البائع
try {
    $stmt = $db->prepare("
        SELECT sp.*, p.name, p.supplier_price, p.suggested_price, p.image_url
        FROM seller_products sp 
        JOIN products p ON sp.product_id = p.id 
        WHERE sp.seller_id = ? 
        ORDER BY sp.added_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$sellerId]);
    $sellerProducts = $stmt->fetchAll();
} catch (PDOException $e) {
    $sellerProducts = [];
}

// جلب آخر الطلبات
try {
    $stmt = $db->prepare("
        SELECT * FROM orders 
        WHERE seller_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$sellerId]);
    $recentOrders = $stmt->fetchAll();
} catch (PDOException $e) {
    $recentOrders = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="https://via.placeholder.com/80x80?text=Admin" class="rounded-circle mb-2" alt="Admin">
                    <h6 class="text-white"><?php echo htmlspecialchars($currentUser['full_name']); ?></h6>
                    <small class="text-white-50">مدير النظام</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sellers.php">
                            <i class="fas fa-users me-2"></i>إدارة البائعين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-boxes me-2"></i>إدارة المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>إدارة الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i>إعدادات الموقع
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link" href="../pages/logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">تفاصيل البائع: <?php echo htmlspecialchars($seller['full_name']); ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="sellers.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>

            <!-- معلومات البائع -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-user me-2"></i>المعلومات الشخصية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>الاسم الكامل:</strong> <?php echo htmlspecialchars($seller['full_name']); ?></p>
                                    <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($seller['username']); ?></p>
                                    <p><strong>البريد الإلكتروني:</strong> <?php echo htmlspecialchars($seller['email']); ?></p>
                                    <p><strong>رقم الهاتف:</strong> <?php echo htmlspecialchars($seller['phone'] ?? 'غير محدد'); ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>المدينة:</strong> <?php echo htmlspecialchars($seller['city'] ?? 'غير محدد'); ?></p>
                                    <p><strong>البلد:</strong> <?php echo htmlspecialchars($seller['country'] ?? 'غير محدد'); ?></p>
                                    <p><strong>تاريخ التسجيل:</strong> <?php echo formatDate($seller['created_at']); ?></p>
                                    <p><strong>الحالة:</strong> 
                                        <span class="badge bg-<?php echo $seller['status'] === 'active' ? 'success' : ($seller['status'] === 'pending' ? 'warning' : 'danger'); ?>">
                                            <?php echo getUserStatusText($seller['status']); ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                            
                            <?php if ($seller['address']): ?>
                                <hr>
                                <p><strong>العنوان:</strong> <?php echo htmlspecialchars($seller['address']); ?></p>
                            <?php endif; ?>
                            
                            <?php if ($seller['business_name'] || $seller['business_type']): ?>
                                <hr>
                                <h6>معلومات النشاط التجاري</h6>
                                <?php if ($seller['business_name']): ?>
                                    <p><strong>اسم النشاط:</strong> <?php echo htmlspecialchars($seller['business_name']); ?></p>
                                <?php endif; ?>
                                <?php if ($seller['business_type']): ?>
                                    <p><strong>نوع النشاط:</strong> <?php echo htmlspecialchars($seller['business_type']); ?></p>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-chart-bar me-2"></i>الإحصائيات</h5>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <h3 class="text-primary"><?php echo $stats['products'] ?? 0; ?></h3>
                                <p class="mb-0">المنتجات</p>
                            </div>
                            <hr>
                            <div class="text-center mb-3">
                                <h3 class="text-success"><?php echo $stats['orders']['total'] ?? 0; ?></h3>
                                <p class="mb-0">إجمالي الطلبات</p>
                            </div>
                            <hr>
                            <div class="text-center mb-3">
                                <h3 class="text-info"><?php echo formatPrice($stats['orders']['total_sales'] ?? 0); ?></h3>
                                <p class="mb-0">إجمالي المبيعات</p>
                            </div>
                            <hr>
                            <div class="text-center">
                                <h3 class="text-warning"><?php echo formatPrice($stats['orders']['total_commission'] ?? 0); ?></h3>
                                <p class="mb-0">إجمالي العمولة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- منتجات البائع -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-boxes me-2"></i>منتجات البائع (آخر 10)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($sellerProducts)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>المنتج</th>
                                                <th>سعر المورد</th>
                                                <th>السعر المقترح</th>
                                                <th>سعر البائع</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الإضافة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($sellerProducts as $product): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <img src="<?php echo htmlspecialchars($product['image_url']); ?>" 
                                                                 alt="<?php echo htmlspecialchars($product['name']); ?>" 
                                                                 class="me-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                            <?php echo htmlspecialchars($product['name']); ?>
                                                        </div>
                                                    </td>
                                                    <td><?php echo formatPrice($product['supplier_price']); ?></td>
                                                    <td><?php echo formatPrice($product['suggested_price']); ?></td>
                                                    <td><?php echo formatPrice($product['selling_price']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $product['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                            <?php echo $product['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo formatDate($product['added_at']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p class="text-muted text-center">لا توجد منتجات</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- آخر الطلبات -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shopping-cart me-2"></i>آخر الطلبات (آخر 10)</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentOrders)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>العميل</th>
                                                <th>المبلغ الإجمالي</th>
                                                <th>العمولة</th>
                                                <th>الحالة</th>
                                                <th>تاريخ الطلب</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentOrders as $order): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                                                    <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                                    <td><?php echo formatPrice($order['total_amount']); ?></td>
                                                    <td><?php echo formatPrice($order['commission_amount']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $order['status'] === 'delivered' ? 'success' : ($order['status'] === 'pending' ? 'warning' : 'info'); ?>">
                                                            <?php echo getOrderStatusText($order['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo formatDate($order['created_at']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p class="text-muted text-center">لا توجد طلبات</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
