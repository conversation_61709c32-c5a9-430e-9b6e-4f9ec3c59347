<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
requireAdmin();

$currentUser = getCurrentUser();

// فلترة الطلبات (نفس المنطق من orders.php)
$statusFilter = $_GET['status'] ?? '';
$sellerFilter = $_GET['seller'] ?? '';
$searchQuery = $_GET['search'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$exportFormat = $_GET['export'] ?? 'csv';

$whereConditions = [];
$params = [];

if ($statusFilter) {
    $whereConditions[] = "o.status = ?";
    $params[] = $statusFilter;
}

if ($sellerFilter) {
    $whereConditions[] = "o.seller_id = ?";
    $params[] = $sellerFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(o.order_number LIKE ? OR o.customer_name LIKE ? OR o.customer_email LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(o.created_at) >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(o.created_at) <= ?";
    $params[] = $dateTo;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// جلب الطلبات
try {
    $stmt = $db->prepare("
        SELECT o.*, u.full_name as seller_name, u.username as seller_username,
               COUNT(oi.id) as items_count,
               GROUP_CONCAT(p.name SEPARATOR ', ') as product_names
        FROM orders o 
        JOIN users u ON o.seller_id = u.id 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        $whereClause 
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ");
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
} catch (PDOException $e) {
    die('حدث خطأ في جلب البيانات');
}

// تحديد نوع التصدير
if ($exportFormat === 'csv') {
    // تصدير CSV
    $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.csv';
    
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // إضافة BOM للدعم العربي في Excel
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // رؤوس الأعمدة
    $headers = [
        'رقم الطلب',
        'اسم العميل',
        'بريد العميل',
        'هاتف العميل',
        'البائع',
        'اسم المستخدم للبائع',
        'عدد المنتجات',
        'أسماء المنتجات',
        'المبلغ الإجمالي',
        'مبلغ العمولة',
        'حالة الطلب',
        'حالة الدفع',
        'طريقة الدفع',
        'رقم التتبع',
        'شركة الشحن',
        'عنوان الشحن',
        'ملاحظات',
        'تاريخ الطلب',
        'آخر تحديث'
    ];
    
    fputcsv($output, $headers);
    
    // بيانات الطلبات
    foreach ($orders as $order) {
        $row = [
            $order['order_number'],
            $order['customer_name'],
            $order['customer_email'],
            $order['customer_phone'] ?? '',
            $order['seller_name'],
            $order['seller_username'],
            $order['items_count'],
            $order['product_names'] ?? '',
            $order['total_amount'],
            $order['commission_amount'],
            getOrderStatusText($order['status']),
            getPaymentStatusText($order['payment_status']),
            $order['payment_method'] ?? '',
            $order['tracking_number'] ?? '',
            $order['shipping_company'] ?? '',
            $order['shipping_address'] ?? '',
            $order['notes'] ?? '',
            $order['created_at'],
            $order['updated_at']
        ];
        
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit;
    
} elseif ($exportFormat === 'excel') {
    // تصدير Excel (HTML table مع headers خاصة)
    $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.xls';
    
    header('Content-Type: application/vnd.ms-excel; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    echo "\xEF\xBB\xBF"; // BOM for UTF-8
    
    ?>
    <html dir="rtl">
    <head>
        <meta charset="UTF-8">
        <style>
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #000; padding: 5px; text-align: right; }
            th { background-color: #f0f0f0; font-weight: bold; }
        </style>
    </head>
    <body>
        <h2>تقرير الطلبات - <?php echo date('Y-m-d H:i:s'); ?></h2>
        <p>تم التصدير بواسطة: <?php echo htmlspecialchars($currentUser['full_name']); ?></p>
        <p>عدد الطلبات: <?php echo count($orders); ?></p>
        
        <table>
            <thead>
                <tr>
                    <th>رقم الطلب</th>
                    <th>اسم العميل</th>
                    <th>بريد العميل</th>
                    <th>هاتف العميل</th>
                    <th>البائع</th>
                    <th>اسم المستخدم للبائع</th>
                    <th>عدد المنتجات</th>
                    <th>المبلغ الإجمالي</th>
                    <th>مبلغ العمولة</th>
                    <th>حالة الطلب</th>
                    <th>حالة الدفع</th>
                    <th>طريقة الدفع</th>
                    <th>رقم التتبع</th>
                    <th>شركة الشحن</th>
                    <th>تاريخ الطلب</th>
                    <th>آخر تحديث</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($orders as $order): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($order['order_number']); ?></td>
                        <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                        <td><?php echo htmlspecialchars($order['customer_email']); ?></td>
                        <td><?php echo htmlspecialchars($order['customer_phone'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars($order['seller_name']); ?></td>
                        <td><?php echo htmlspecialchars($order['seller_username']); ?></td>
                        <td><?php echo $order['items_count']; ?></td>
                        <td><?php echo $order['total_amount']; ?></td>
                        <td><?php echo $order['commission_amount']; ?></td>
                        <td><?php echo getOrderStatusText($order['status']); ?></td>
                        <td><?php echo getPaymentStatusText($order['payment_status']); ?></td>
                        <td><?php echo htmlspecialchars($order['payment_method'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars($order['tracking_number'] ?? ''); ?></td>
                        <td><?php echo htmlspecialchars($order['shipping_company'] ?? ''); ?></td>
                        <td><?php echo $order['created_at']; ?></td>
                        <td><?php echo $order['updated_at']; ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <br>
        <p><strong>ملاحظات:</strong></p>
        <ul>
            <li>تم تصدير البيانات في: <?php echo date('Y-m-d H:i:s'); ?></li>
            <li>المبالغ بالريال السعودي</li>
            <li>التواريخ بالتوقيت المحلي</li>
        </ul>
    </body>
    </html>
    <?php
    exit;
    
} elseif ($exportFormat === 'pdf') {
    // تصدير PDF (يتطلب مكتبة PDF)
    // للبساطة، سنعرض رسالة أن هذه الميزة قيد التطوير
    die('تصدير PDF قيد التطوير. يرجى استخدام CSV أو Excel.');
    
} else {
    // تصدير JSON
    $filename = 'orders_export_' . date('Y-m-d_H-i-s') . '.json';
    
    header('Content-Type: application/json; charset=UTF-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    $exportData = [
        'export_info' => [
            'exported_at' => date('Y-m-d H:i:s'),
            'exported_by' => $currentUser['full_name'],
            'total_orders' => count($orders),
            'filters_applied' => [
                'status' => $statusFilter,
                'seller' => $sellerFilter,
                'search' => $searchQuery,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ],
        'orders' => []
    ];
    
    foreach ($orders as $order) {
        $exportData['orders'][] = [
            'order_number' => $order['order_number'],
            'customer' => [
                'name' => $order['customer_name'],
                'email' => $order['customer_email'],
                'phone' => $order['customer_phone']
            ],
            'seller' => [
                'name' => $order['seller_name'],
                'username' => $order['seller_username']
            ],
            'items_count' => (int)$order['items_count'],
            'product_names' => $order['product_names'],
            'amounts' => [
                'total' => (float)$order['total_amount'],
                'commission' => (float)$order['commission_amount']
            ],
            'status' => [
                'order' => $order['status'],
                'payment' => $order['payment_status']
            ],
            'payment_method' => $order['payment_method'],
            'shipping' => [
                'tracking_number' => $order['tracking_number'],
                'company' => $order['shipping_company'],
                'address' => $order['shipping_address']
            ],
            'notes' => $order['notes'],
            'dates' => [
                'created_at' => $order['created_at'],
                'updated_at' => $order['updated_at']
            ]
        ];
    }
    
    echo json_encode($exportData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}
?>
