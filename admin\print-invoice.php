<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات المدير
requireAdmin();

$invoiceId = (int)($_GET['id'] ?? 0);

if ($invoiceId <= 0) {
    die('معرف الفاتورة غير صحيح');
}

// جلب تفاصيل الفاتورة
try {
    $stmt = $db->prepare("
        SELECT i.*, u.full_name as seller_name, u.username as seller_username,
               u.email as seller_email, u.phone as seller_phone, u.address as seller_address,
               u.business_name, u.tax_number, u.bank_account,
               admin.full_name as created_by_name
        FROM invoices i 
        JOIN users u ON i.seller_id = u.id 
        JOIN users admin ON i.created_by = admin.id
        WHERE i.id = ?
    ");
    $stmt->execute([$invoiceId]);
    $invoice = $stmt->fetch();
    
    if (!$invoice) {
        die('الفاتورة غير موجودة');
    }
} catch (PDOException $e) {
    die('حدث خطأ في جلب بيانات الفاتورة');
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة الفاتورة - <?php echo htmlspecialchars($invoice['invoice_number']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: white;
            color: #333;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .invoice-header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-info {
            text-align: right;
        }
        
        .invoice-title {
            text-align: left;
            color: #007bff;
        }
        
        .invoice-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .amount-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 5px;
            border: 2px solid #007bff;
        }
        
        .total-amount {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .invoice-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- أزرار التحكم -->
        <div class="no-print text-center mb-4">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print me-2"></i>طباعة الفاتورة
            </button>
            <button onclick="window.close()" class="btn btn-secondary ms-2">
                <i class="fas fa-times me-2"></i>إغلاق
            </button>
        </div>

        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="row">
                <div class="col-md-6 company-info">
                    <h2 class="text-primary mb-3">منصة الدروب شيبنج</h2>
                    <p class="mb-1"><i class="fas fa-map-marker-alt me-2"></i>المملكة العربية السعودية</p>
                    <p class="mb-1"><i class="fas fa-envelope me-2"></i><EMAIL></p>
                    <p class="mb-1"><i class="fas fa-phone me-2"></i>+966 50 123 4567</p>
                    <p class="mb-0"><i class="fas fa-globe me-2"></i>www.dropshipping.com</p>
                </div>
                <div class="col-md-6 invoice-title">
                    <h1 class="display-4 text-primary">فاتورة</h1>
                    <h3 class="text-muted"><?php echo htmlspecialchars($invoice['invoice_number']); ?></h3>
                </div>
            </div>
        </div>

        <!-- معلومات الفاتورة -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="invoice-details">
                    <h5 class="text-primary mb-3">معلومات الفاتورة</h5>
                    <div class="row mb-2">
                        <div class="col-5"><strong>رقم الفاتورة:</strong></div>
                        <div class="col-7"><?php echo htmlspecialchars($invoice['invoice_number']); ?></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-5"><strong>تاريخ الإصدار:</strong></div>
                        <div class="col-7"><?php echo date('d/m/Y', strtotime($invoice['issue_date'])); ?></div>
                    </div>
                    <?php if ($invoice['due_date']): ?>
                        <div class="row mb-2">
                            <div class="col-5"><strong>تاريخ الاستحقاق:</strong></div>
                            <div class="col-7"><?php echo date('d/m/Y', strtotime($invoice['due_date'])); ?></div>
                        </div>
                    <?php endif; ?>
                    <div class="row mb-2">
                        <div class="col-5"><strong>نوع الفاتورة:</strong></div>
                        <div class="col-7">
                            <span class="badge bg-<?php 
                                echo $invoice['type'] === 'commission' ? 'info' : 
                                    ($invoice['type'] === 'payout' ? 'success' : 'secondary'); 
                            ?>">
                                <?php echo getInvoiceTypeText($invoice['type']); ?>
                            </span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-5"><strong>الحالة:</strong></div>
                        <div class="col-7">
                            <span class="badge bg-<?php 
                                echo $invoice['status'] === 'paid' ? 'success' : 
                                    ($invoice['status'] === 'pending' ? 'warning' : 'danger'); 
                            ?>">
                                <?php echo getInvoiceStatusText($invoice['status']); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="invoice-details">
                    <h5 class="text-primary mb-3">معلومات البائع</h5>
                    <div class="row mb-2">
                        <div class="col-4"><strong>الاسم:</strong></div>
                        <div class="col-8"><?php echo htmlspecialchars($invoice['seller_name']); ?></div>
                    </div>
                    <?php if ($invoice['business_name']): ?>
                        <div class="row mb-2">
                            <div class="col-4"><strong>النشاط التجاري:</strong></div>
                            <div class="col-8"><?php echo htmlspecialchars($invoice['business_name']); ?></div>
                        </div>
                    <?php endif; ?>
                    <div class="row mb-2">
                        <div class="col-4"><strong>اسم المستخدم:</strong></div>
                        <div class="col-8">@<?php echo htmlspecialchars($invoice['seller_username']); ?></div>
                    </div>
                    <div class="row mb-2">
                        <div class="col-4"><strong>البريد الإلكتروني:</strong></div>
                        <div class="col-8"><?php echo htmlspecialchars($invoice['seller_email']); ?></div>
                    </div>
                    <?php if ($invoice['seller_phone']): ?>
                        <div class="row mb-2">
                            <div class="col-4"><strong>الهاتف:</strong></div>
                            <div class="col-8"><?php echo htmlspecialchars($invoice['seller_phone']); ?></div>
                        </div>
                    <?php endif; ?>
                    <?php if ($invoice['tax_number']): ?>
                        <div class="row">
                            <div class="col-4"><strong>الرقم الضريبي:</strong></div>
                            <div class="col-8"><?php echo htmlspecialchars($invoice['tax_number']); ?></div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- تفاصيل المبلغ -->
        <div class="amount-section mb-4">
            <h5 class="text-primary mb-3">تفاصيل المبلغ</h5>
            <div class="table-responsive">
                <table class="table table-borderless">
                    <tbody>
                        <tr>
                            <td class="fs-5">
                                <?php if ($invoice['description']): ?>
                                    <?php echo htmlspecialchars($invoice['description']); ?>
                                <?php else: ?>
                                    <?php echo getInvoiceTypeText($invoice['type']); ?>
                                <?php endif; ?>
                            </td>
                            <td class="text-end">
                                <span class="total-amount"><?php echo formatPrice($invoice['amount']); ?></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <hr>
            <div class="row">
                <div class="col-6">
                    <h4>المجموع الإجمالي:</h4>
                </div>
                <div class="col-6 text-end">
                    <h3 class="total-amount"><?php echo formatPrice($invoice['amount']); ?></h3>
                </div>
            </div>
        </div>

        <!-- الوصف التفصيلي -->
        <?php if ($invoice['description']): ?>
            <div class="mb-4">
                <h5 class="text-primary">ملاحظات إضافية:</h5>
                <div class="border p-3 rounded bg-light">
                    <?php echo nl2br(htmlspecialchars($invoice['description'])); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- معلومات إضافية -->
        <div class="row mb-4">
            <div class="col-md-6">
                <h6 class="text-muted">معلومات إضافية:</h6>
                <p class="small text-muted mb-1">أنشأها: <?php echo htmlspecialchars($invoice['created_by_name']); ?></p>
                <p class="small text-muted mb-0">تاريخ الإنشاء: <?php echo formatDate($invoice['created_at']); ?></p>
            </div>
            <?php if ($invoice['bank_account']): ?>
                <div class="col-md-6">
                    <h6 class="text-muted">معلومات الدفع:</h6>
                    <p class="small text-muted mb-0">رقم الحساب البنكي: <?php echo htmlspecialchars($invoice['bank_account']); ?></p>
                </div>
            <?php endif; ?>
        </div>

        <!-- تذييل الفاتورة -->
        <div class="text-center border-top pt-3">
            <p class="text-muted mb-1">شكراً لك على التعامل معنا</p>
            <p class="text-muted mb-0 small">هذه فاتورة إلكترونية ولا تحتاج إلى توقيع</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html>
