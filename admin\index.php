<?php
// بدء الجلسة
session_start();

// تضمين قاعدة البيانات
require_once '../config/database.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
    header('Location: ../pages/login.php');
    exit();
}

if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../pages/dashboard.php');
    exit();
}

$pageTitle = 'لوحة تحكم المدير';

// دوال مساعدة بسيطة
function formatPrice($price) {
    return number_format($price, 2) . ' د.م';
}

function formatDate($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// الحصول على الإحصائيات البسيطة
function getSimpleAdminStats() {
    global $db;

    try {
        $stats = [];

        // عدد البائعين
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM users WHERE role = 'seller'");
        $stmt->execute();
        $stats['sellers'] = $stmt->fetch(PDO::FETCH_ASSOC);

        // عدد المنتجات
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM products");
        $stmt->execute();
        $stats['products'] = $stmt->fetch(PDO::FETCH_ASSOC);

        // عدد الطلبات
        $stmt = $db->prepare("SELECT COUNT(*) as total, SUM(total_amount) as total_sales FROM orders");
        $stmt->execute();
        $stats['orders'] = $stmt->fetch(PDO::FETCH_ASSOC);

        // الطلبات حسب الحالة
        $stmt = $db->prepare("SELECT
                             SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                             SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
                             SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                             SUM(CASE WHEN status = 'shipped' THEN 1 ELSE 0 END) as shipped,
                             SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                             SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                             FROM orders");
        $stmt->execute();
        $stats['order_status'] = $stmt->fetch(PDO::FETCH_ASSOC);

        return $stats;
    } catch (PDOException $e) {
        return false;
    }
}

$stats = getSimpleAdminStats();

// CSS إضافي خاص بهذه الصفحة
$additional_css = '
/* تخصيصات إضافية خاصة بالصفحة الرئيسية */
.dashboard-welcome {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.orders-overview {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 30px;
}

.order-status-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid;
    position: relative;
    overflow: hidden;
}

.order-status-card::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent 0%, rgba(255,255,255,0.1) 100%);
    pointer-events: none;
}

.order-status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.order-status-card.pending {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
}

.order-status-card.confirmed {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #e0f7fa 0%, #ffffff 100%);
}

.order-status-card.processing {
    border-left-color: #fd7e14;
    background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
}

.order-status-card.shipped {
    border-left-color: #6f42c1;
    background: linear-gradient(135deg, #f3e5f5 0%, #ffffff 100%);
}

.order-status-card.delivered {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #e8f5e8 0%, #ffffff 100%);
}

.order-status-card.cancelled {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #ffebee 0%, #ffffff 100%);
}

.order-status-card.returned {
    border-left-color: #6c757d;
    background: linear-gradient(135deg, #f5f5f5 0%, #ffffff 100%);
}

.order-status-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: 15px;
}

.order-status-icon.pending { background: #ffc107; }
.order-status-icon.confirmed { background: #17a2b8; }
.order-status-icon.processing { background: #fd7e14; }
.order-status-icon.shipped { background: #6f42c1; }
.order-status-icon.delivered { background: #28a745; }
.order-status-icon.cancelled { background: #dc3545; }
.order-status-icon.returned { background: #6c757d; }

.order-count {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
    color: #2c3e50;
}

.order-label {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.order-percentage {
    font-size: 0.9rem;
    font-weight: 500;
}

.order-percentage.positive {
    color: #28a745;
}

.order-percentage.negative {
    color: #dc3545;
}

.order-percentage.neutral {
    color: #6c757d;
}

.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-top: 30px;
}

.quick-action-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    text-decoration: none;
    display: inline-block;
    margin: 5px;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    color: white;
}

.notification-item {
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-icon {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.stats-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
}

.stats-summary .row {
    text-align: center;
}

.stats-summary h4 {
    margin-bottom: 5px;
}

.stats-summary small {
    opacity: 0.8;
}

/* تصميم الفلاتر */
.dashboard-filters {
    background: white;
    border-radius: 12px;
    padding: 0 20px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.06);
    margin-bottom: 25px;
    border-left: 3px solid var(--primary-color);
    height: 70px;
    display: flex;
    align-items: center;
}

.dashboard-filters .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.dashboard-filters .form-control {
    border-radius: 6px;
    border: 1px solid #e0e6ed;
    padding: 8px 12px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.dashboard-filters .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.advanced-filters .btn-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}

.advanced-filters .btn-link:hover {
    color: var(--secondary-color);
}

.advanced-filters .card {
    border: 1px solid #e0e6ed;
    border-radius: 10px;
}

.advanced-filters .card-body {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.filter-active {
    background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
    border-left-color: #2196f3 !important;
}

.filter-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-summary {
    background: #e8f5e8;
    border: 1px solid #c3e6c3;
    border-radius: 8px;
    padding: 10px 15px;
    margin-top: 15px;
    font-size: 0.9rem;
    color: #155724;
}

.filter-tag {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin: 2px;
}

.filter-tag .remove {
    margin-right: 5px;
    cursor: pointer;
    opacity: 0.8;
}

.filter-tag .remove:hover {
    opacity: 1;
}

.view-toggle {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
}

.view-toggle .btn-group .btn {
    border-radius: 8px;
    margin: 0 2px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تصميم أفضل المنتجات */
.product-item {
    padding: 15px;
    border-radius: 10px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.product-item:hover {
    background: #e9ecef;
    transform: translateX(-5px);
}

.product-stats .badge {
    font-size: 0.7rem;
}

.rank-badge {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 0.9rem;
}

.rank-1 {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.rank-2 {
    background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
    box-shadow: 0 4px 15px rgba(192, 192, 192, 0.4);
}

.rank-3 {
    background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
    box-shadow: 0 4px 15px rgba(205, 127, 50, 0.4);
}

.rank-other {
    background: linear-gradient(135deg, #6c757d 0%, #868e96 100%);
}

/* تصميم أفضل البائعين */
.seller-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.seller-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.seller-card:nth-child(1) { border-left-color: #ffd700; }
.seller-card:nth-child(2) { border-left-color: #c0c0c0; }
.seller-card:nth-child(3) { border-left-color: #cd7f32; }

.seller-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.seller-stats {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
}

.seller-stats strong {
    font-size: 1.1rem;
}

.seller-stats small {
    font-size: 0.7rem;
    color: #6c757d;
}

.progress {
    background-color: #e9ecef;
    border-radius: 10px;
}

.progress-bar {
    border-radius: 10px;
}

/* تحسينات إضافية */
.stats-card h6 {
    color: #2c3e50;
    font-weight: 600;
}

.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
';

// JavaScript إضافي خاص بهذه الصفحة
$additional_js = '
// متغيرات الفلاتر
let currentFilters = {
    period: "month",
    status: "all",
    seller: "all",
    dateFrom: "",
    dateTo: "",
    amountFrom: "",
    amountTo: "",
    city: "all",
    paymentMethod: "all",
    viewType: "cards",
    sortBy: "date"
};

let isLoading = false;

// تحديث إحصائيات لوحة التحكم
function updateDashboardStats() {
    if (isLoading) return;

    const period = document.getElementById("stats-period").value;
    currentFilters.period = period;

    showLoading();

    // محاكاة تحديث البيانات
    setTimeout(() => {
        updateStatsDisplay();
        hideLoading();
        showFilterSummary();
    }, 1000);
}

// تحديث فلتر الطلبات
function updateOrdersFilter() {
    const status = document.getElementById("order-status").value;
    currentFilters.status = status;

    // تحديث عرض بطاقات الطلبات
    updateOrderCardsDisplay();
    showFilterSummary();
}

// تحديث فلتر البائعين
function updateSellerFilter() {
    const seller = document.getElementById("seller-filter").value;
    currentFilters.seller = seller;

    showFilterSummary();
}

// تطبيق جميع الفلاتر
function applyFilters() {
    if (isLoading) return;

    // جمع قيم الفلاتر
    currentFilters.dateFrom = document.getElementById("date-from").value;
    currentFilters.dateTo = document.getElementById("date-to").value;
    currentFilters.amountFrom = document.getElementById("amount-from").value;
    currentFilters.amountTo = document.getElementById("amount-to").value;
    currentFilters.city = document.getElementById("city-filter").value;
    currentFilters.paymentMethod = document.getElementById("payment-method").value;
    currentFilters.sortBy = document.getElementById("sort-by").value;

    showLoading();

    // إضافة كلاس نشط للفلاتر
    document.querySelector(".dashboard-filters").classList.add("filter-active");

    // محاكاة تطبيق الفلاتر
    setTimeout(() => {
        updateAllDisplays();
        hideLoading();
        showFilterSummary();
        showSuccessMessage("تم تطبيق الفلاتر بنجاح");
    }, 1500);
}

// إعادة تعيين الفلاتر
function resetFilters() {
    // إعادة تعيين القيم
    document.getElementById("stats-period").value = "month";
    document.getElementById("order-status").value = "all";
    document.getElementById("seller-filter").value = "all";
    document.getElementById("date-from").value = "";
    document.getElementById("date-to").value = "";
    document.getElementById("amount-from").value = "";
    document.getElementById("amount-to").value = "";
    document.getElementById("city-filter").value = "all";
    document.getElementById("payment-method").value = "all";
    document.getElementById("view-type").value = "cards";
    document.getElementById("sort-by").value = "date";

    // إعادة تعيين المتغيرات
    currentFilters = {
        period: "month",
        status: "all",
        seller: "all",
        dateFrom: "",
        dateTo: "",
        amountFrom: "",
        amountTo: "",
        city: "all",
        paymentMethod: "all",
        viewType: "cards",
        sortBy: "date"
    };

    // إزالة كلاس النشط
    document.querySelector(".dashboard-filters").classList.remove("filter-active");

    // تحديث العرض
    updateAllDisplays();
    hideFilterSummary();
    showSuccessMessage("تم إعادة تعيين الفلاتر");
}

// تغيير نوع العرض
function changeViewType() {
    const viewType = document.getElementById("view-type").value;
    currentFilters.viewType = viewType;

    // تحديث العرض حسب النوع المختار
    updateViewDisplay(viewType);
}

// تصدير بيانات لوحة التحكم
function exportDashboardData() {
    showLoading();

    // محاكاة تصدير البيانات
    setTimeout(() => {
        hideLoading();

        // إنشاء رابط تحميل وهمي
        const link = document.createElement("a");
        link.href = "export-dashboard.php?" + new URLSearchParams(currentFilters).toString();
        link.download = "dashboard-data-" + new Date().toISOString().split("T")[0] + ".xlsx";
        link.click();

        showSuccessMessage("تم تصدير البيانات بنجاح");
    }, 1000);
}

// عرض حالة التحميل
function showLoading() {
    isLoading = true;

    // إضافة overlay للتحميل
    const overlay = document.createElement("div");
    overlay.className = "loading-overlay";
    overlay.innerHTML = `<div class="loading-spinner"></div>`;

    document.querySelector(".stats-summary").style.position = "relative";
    document.querySelector(".stats-summary").appendChild(overlay);
}

// إخفاء حالة التحميل
function hideLoading() {
    isLoading = false;

    const overlay = document.querySelector(".loading-overlay");
    if (overlay) {
        overlay.remove();
    }
}

// تحديث عرض الإحصائيات
function updateStatsDisplay() {
    // محاكاة تحديث الأرقام حسب الفترة
    const period = currentFilters.period;
    let multiplier = 1;

    switch(period) {
        case "today": multiplier = 0.1; break;
        case "week": multiplier = 0.3; break;
        case "month": multiplier = 1; break;
        case "quarter": multiplier = 3; break;
        case "year": multiplier = 12; break;
        case "all": multiplier = 24; break;
    }

    // تحديث الأرقام (محاكاة)
    const baseNumbers = [15750, 1575, 14175, 15750];
    const elements = document.querySelectorAll(".stats-summary h4");

    elements.forEach((element, index) => {
        if (baseNumbers[index]) {
            const newValue = (baseNumbers[index] * multiplier).toFixed(2);
            element.textContent = new Intl.NumberFormat("ar-MA").format(newValue) + " د.م";
        }
    });
}

// تحديث عرض بطاقات الطلبات
function updateOrderCardsDisplay() {
    const status = currentFilters.status;
    const cards = document.querySelectorAll(".order-status-card");

    cards.forEach(card => {
        if (status === "all") {
            card.style.display = "block";
            card.style.opacity = "1";
            card.style.transform = "scale(1)";
        } else {
            if (card.classList.contains(status)) {
                card.style.display = "block";
                card.style.opacity = "1";
                card.style.transform = "scale(1.05)";
                setTimeout(() => {
                    card.style.transform = "scale(1)";
                }, 300);
            } else {
                card.style.opacity = "0.3";
                card.style.transform = "scale(0.95)";
            }
        }
    });
}

// عرض ملخص الفلاتر المطبقة
function showFilterSummary() {
    let summary = [];

    if (currentFilters.period !== "month") {
        summary.push(`الفترة: ${getPeriodText(currentFilters.period)}`);
    }

    if (currentFilters.status !== "all") {
        summary.push(`الحالة: ${getStatusText(currentFilters.status)}`);
    }

    if (currentFilters.seller !== "all") {
        summary.push(`البائع: ${getSellerText(currentFilters.seller)}`);
    }

    if (currentFilters.dateFrom) {
        summary.push(`من: ${currentFilters.dateFrom}`);
    }

    if (currentFilters.dateTo) {
        summary.push(`إلى: ${currentFilters.dateTo}`);
    }

    if (summary.length > 0) {
        const summaryDiv = document.getElementById("filter-summary") || createFilterSummary();
        summaryDiv.innerHTML = `
            <strong>الفلاتر المطبقة:</strong>
            ${summary.map(item => `<span class="filter-tag">${item}</span>`).join("")}
        `;
        summaryDiv.style.display = "block";
    }
}

// إخفاء ملخص الفلاتر
function hideFilterSummary() {
    const summaryDiv = document.getElementById("filter-summary");
    if (summaryDiv) {
        summaryDiv.style.display = "none";
    }
}

// إنشاء عنصر ملخص الفلاتر
function createFilterSummary() {
    const summaryDiv = document.createElement("div");
    summaryDiv.id = "filter-summary";
    summaryDiv.className = "filter-summary";
    summaryDiv.style.display = "none";

    document.querySelector(".dashboard-filters").appendChild(summaryDiv);
    return summaryDiv;
}

// دوال مساعدة للنصوص
function getPeriodText(period) {
    const texts = {
        "today": "اليوم",
        "week": "آخر 7 أيام",
        "month": "هذا الشهر",
        "quarter": "آخر 3 شهور",
        "year": "هذا العام",
        "all": "جميع الفترات"
    };
    return texts[period] || period;
}

function getStatusText(status) {
    const texts = {
        "pending": "معلقة",
        "confirmed": "مؤكدة",
        "processing": "قيد التجهيز",
        "shipped": "مشحونة",
        "delivered": "مسلمة",
        "cancelled": "ملغاة",
        "returned": "مرتجعة"
    };
    return texts[status] || status;
}

function getSellerText(seller) {
    const texts = {
        "active": "البائعين النشطين",
        "pending": "في انتظار الموافقة",
        "top": "أفضل البائعين"
    };
    return texts[seller] || seller;
}

// تحديث جميع العروض
function updateAllDisplays() {
    updateStatsDisplay();
    updateOrderCardsDisplay();
}

// عرض رسالة نجاح
function showSuccessMessage(message) {
    // إنشاء toast notification
    const toast = document.createElement("div");
    toast.className = "position-fixed top-0 end-0 p-3";
    toast.style.zIndex = "9999";
    toast.innerHTML = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.appendChild(toast);

    // إزالة التنبيه بعد 3 ثوان
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// إضافة تأثيرات للإشعارات
document.addEventListener("DOMContentLoaded", function() {
    const notificationItems = document.querySelectorAll(".notification-item");
    notificationItems.forEach((item, index) => {
        item.style.opacity = "0";
        item.style.transform = "translateX(20px)";
        setTimeout(() => {
            item.style.transition = "all 0.5s ease";
            item.style.opacity = "1";
            item.style.transform = "translateX(0)";
        }, index * 200);
    });

    // إنشاء عنصر ملخص الفلاتر
    createFilterSummary();

    // تأثيرات تفاعلية للبطاقات
    const cards = document.querySelectorAll(".order-status-card");
    cards.forEach(card => {
        card.addEventListener("mouseenter", function() {
            if (this.style.opacity !== "0.3") {
                this.style.transform = "translateY(-5px)";
            }
        });

        card.addEventListener("mouseleave", function() {
            if (this.style.opacity !== "0.3") {
                this.style.transform = "translateY(0)";
            }
        });
    });
});
';

// تضمين header المدير
include 'includes/admin_header.php';
?>

        <h1 class="page-title">مرحباً بك في لوحة تحكم المدير</h1>
        <p class="welcome-text">مرحباً <?php echo htmlspecialchars($user['full_name']); ?>، إليك نظرة عامة على النظام</p>

        <!-- Filters -->
        <div class="dashboard-filters">
            <div class="row g-3 align-items-center w-100">
                <div class="col-md-2">
                    <select class="form-control" id="stats-period" onchange="updateDashboardStats()">
                        <option value="today">اليوم</option>
                        <option value="week">آخر 7 أيام</option>
                        <option value="month" selected>هذا الشهر</option>
                        <option value="quarter">آخر 3 شهور</option>
                        <option value="year">هذا العام</option>
                        <option value="all">جميع الفترات</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <select class="form-control" id="order-status" onchange="updateOrdersFilter()">
                        <option value="all">جميع الحالات</option>
                        <option value="pending">معلقة</option>
                        <option value="confirmed">مؤكدة</option>
                        <option value="processing">قيد التجهيز</option>
                        <option value="shipped">مشحونة</option>
                        <option value="delivered">مسلمة</option>
                        <option value="cancelled">ملغاة</option>
                        <option value="returned">مرتجعة</option>
                    </select>
                </div>

                <div class="col-md-2">
                    <select class="form-control" id="seller-filter" onchange="updateSellerFilter()">
                        <option value="all">جميع البائعين</option>
                        <option value="active">البائعين النشطين</option>
                        <option value="pending">في انتظار الموافقة</option>
                        <option value="top">أفضل البائعين</option>
                    </select>
                </div>

                <div class="col-md-4">
                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-primary btn-sm" onclick="applyFilters()">
                            <i class="fas fa-filter me-1"></i>تطبيق
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetFilters()">
                            <i class="fas fa-undo me-1"></i>إعادة تعيين
                        </button>
                        <button type="button" class="btn btn-success btn-sm" onclick="exportDashboardData()">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                    </div>
                </div>

                <div class="col-md-2">
                    <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters">
                        <i class="fas fa-chevron-down me-1"></i>فلاتر متقدمة
                    </button>
                </div>
            </div>
        </div>

        <!-- Advanced Filters (Collapsible) -->
        <div class="advanced-filters">
            <div class="collapse" id="advancedFilters">
                <div class="card card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date-from" value="<?php echo date('Y-m-01'); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date-to" value="<?php echo date('Y-m-d'); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">مبلغ الطلب (من)</label>
                            <input type="number" class="form-control" id="amount-from" placeholder="0.00" step="0.01">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">مبلغ الطلب (إلى)</label>
                            <input type="number" class="form-control" id="amount-to" placeholder="1000.00" step="0.01">
                        </div>
                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-md-3">
                            <label class="form-label">المدينة</label>
                            <select class="form-control" id="city-filter">
                                <option value="all">جميع المدن</option>
                                <option value="casablanca">الدار البيضاء</option>
                                <option value="rabat">الرباط</option>
                                <option value="marrakech">مراكش</option>
                                <option value="fes">فاس</option>
                                <option value="tangier">طنجة</option>
                                <option value="agadir">أكادير</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">طريقة الدفع</label>
                            <select class="form-control" id="payment-method">
                                <option value="all">جميع الطرق</option>
                                <option value="cash">الدفع عند التسليم</option>
                                <option value="card">بطاقة ائتمان</option>
                                <option value="bank">تحويل بنكي</option>
                                <option value="mobile">محفظة إلكترونية</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع العرض</label>
                            <select class="form-control" id="view-type" onchange="changeViewType()">
                                <option value="cards">بطاقات</option>
                                <option value="table">جدول</option>
                                <option value="chart">رسوم بيانية</option>
                                <option value="summary">ملخص</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">ترتيب حسب</label>
                            <select class="form-control" id="sort-by">
                                <option value="date">التاريخ</option>
                                <option value="amount">المبلغ</option>
                                <option value="status">الحالة</option>
                                <option value="seller">البائع</option>
                                <option value="profit">الربح</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملخص سريع للإحصائيات -->
        <div class="stats-summary">
            <div class="row">
                <div class="col-md-3">
                    <h4><?php echo formatPrice($stats['orders']['total_sales'] ?? 0); ?></h4>
                    <small>إجمالي المبيعات</small>
                    <div class="mt-1">
                        <span class="badge bg-light text-dark">
                            <?php echo $stats['orders']['total'] ?? 0; ?> طلب
                        </span>
                    </div>
                </div>
                <div class="col-md-3">
                    <h4><?php echo $stats['sellers']['total'] ?? 0; ?></h4>
                    <small>عدد البائعين</small>
                    <div class="mt-1">
                        <span class="badge bg-light text-dark">
                            مسجلين في النظام
                        </span>
                    </div>
                </div>
                <div class="col-md-3">
                    <h4><?php echo $stats['products']['total'] ?? 0; ?></h4>
                    <small>عدد المنتجات</small>
                    <div class="mt-1">
                        <span class="badge bg-light text-dark">
                            في المتجر
                        </span>
                    </div>
                </div>
                <div class="col-md-3">
                    <h4><?php echo $stats['order_status']['delivered'] ?? 0; ?></h4>
                    <small>طلبات مسلمة</small>
                    <div class="mt-1">
                        <span class="badge bg-light text-dark">
                            تم إتمامها
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات حالات الطلبات -->
        <div class="orders-overview">
            <h5 class="mb-4">
                <i class="fas fa-shopping-cart me-2"></i>
                حالات الطلبات
                <small class="text-muted">(جميع الطلبات المدخلة من البائعين)</small>
            </h5>

            <div class="row g-4">
                <!-- طلبات معلقة -->
                <div class="col-xl-3 col-md-6">
                    <div class="order-status-card pending">
                        <div class="order-status-icon pending">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="order-count"><?php echo $stats['order_status']['pending'] ?? 0; ?></div>
                        <div class="order-label">طلبات معلقة</div>
                        <div class="order-percentage neutral">
                            <i class="fas fa-info-circle me-1"></i>
                            في انتظار المراجعة
                        </div>
                        <div class="mt-2">
                            <a href="orders.php?status=pending" class="btn btn-sm btn-warning">
                                <i class="fas fa-eye me-1"></i>عرض الطلبات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- طلبات مؤكدة -->
                <div class="col-xl-3 col-md-6">
                    <div class="order-status-card confirmed">
                        <div class="order-status-icon confirmed">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="order-count"><?php echo $stats['order_status']['confirmed'] ?? 0; ?></div>
                        <div class="order-label">طلبات مؤكدة</div>
                        <div class="order-percentage positive">
                            <i class="fas fa-thumbs-up me-1"></i>
                            تم تأكيدها
                        </div>
                        <div class="mt-2">
                            <a href="orders.php?status=confirmed" class="btn btn-sm btn-info">
                                <i class="fas fa-eye me-1"></i>عرض الطلبات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- طلبات قيد التجهيز -->
                <div class="col-xl-3 col-md-6">
                    <div class="order-status-card processing">
                        <div class="order-status-icon processing">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="order-count"><?php echo $stats['order_status']['processing'] ?? 0; ?></div>
                        <div class="order-label">قيد التجهيز</div>
                        <div class="order-percentage neutral">
                            <i class="fas fa-spinner me-1"></i>
                            يتم تجهيزها
                        </div>
                        <div class="mt-2">
                            <a href="orders.php?status=processing" class="btn btn-sm btn-warning">
                                <i class="fas fa-eye me-1"></i>عرض الطلبات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- طلبات مشحونة -->
                <div class="col-xl-3 col-md-6">
                    <div class="order-status-card shipped">
                        <div class="order-status-icon shipped">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <div class="order-count"><?php echo $stats['order_status']['shipped'] ?? 0; ?></div>
                        <div class="order-label">طلبات مشحونة</div>
                        <div class="order-percentage positive">
                            <i class="fas fa-truck me-1"></i>
                            في الطريق
                        </div>
                        <div class="mt-2">
                            <a href="orders.php?status=shipped" class="btn btn-sm btn-purple">
                                <i class="fas fa-eye me-1"></i>عرض الطلبات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- طلبات مسلمة -->
                <div class="col-xl-3 col-md-6">
                    <div class="order-status-card delivered">
                        <div class="order-status-icon delivered">
                            <i class="fas fa-check-double"></i>
                        </div>
                        <div class="order-count"><?php echo $stats['order_status']['delivered'] ?? 0; ?></div>
                        <div class="order-label">طلبات مسلمة</div>
                        <div class="order-percentage positive">
                            <i class="fas fa-medal me-1"></i>
                            تم التسليم
                        </div>
                        <div class="mt-2">
                            <a href="orders.php?status=delivered" class="btn btn-sm btn-success">
                                <i class="fas fa-eye me-1"></i>عرض الطلبات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- طلبات ملغاة -->
                <div class="col-xl-3 col-md-6">
                    <div class="order-status-card cancelled">
                        <div class="order-status-icon cancelled">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="order-count"><?php echo $stats['order_status']['cancelled'] ?? 0; ?></div>
                        <div class="order-label">طلبات ملغاة</div>
                        <div class="order-percentage negative">
                            <i class="fas fa-ban me-1"></i>
                            تم إلغاؤها
                        </div>
                        <div class="mt-2">
                            <a href="orders.php?status=cancelled" class="btn btn-sm btn-danger">
                                <i class="fas fa-eye me-1"></i>عرض الطلبات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- طلبات مرتجعة -->
                <div class="col-xl-3 col-md-6">
                    <div class="order-status-card returned">
                        <div class="order-status-icon returned">
                            <i class="fas fa-undo"></i>
                        </div>
                        <div class="order-count">0</div>
                        <div class="order-label">طلبات مرتجعة</div>
                        <div class="order-percentage negative">
                            <i class="fas fa-exchange-alt me-1"></i>
                            تم إرجاعها
                        </div>
                        <div class="mt-2">
                            <a href="orders.php?status=returned" class="btn btn-sm btn-secondary">
                                <i class="fas fa-eye me-1"></i>عرض الطلبات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- إجمالي الطلبات -->
                <div class="col-xl-3 col-md-6">
                    <div class="order-status-card" style="border-left-color: #667eea; background: linear-gradient(135deg, #e8f0fe 0%, #ffffff 100%);">
                        <div class="order-status-icon" style="background: #667eea;">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="order-count"><?php echo $stats['orders']['total'] ?? 0; ?></div>
                        <div class="order-label">إجمالي الطلبات</div>
                        <div class="order-percentage positive">
                            <i class="fas fa-chart-line me-1"></i>
                            جميع الحالات
                        </div>
                        <div class="mt-2">
                            <a href="orders.php" class="btn btn-sm btn-primary">
                                <i class="fas fa-list me-1"></i>عرض الكل
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات تفصيلية -->
        <div class="row g-4 mt-4">
            <div class="col-lg-12">
                <div class="stats-card">
                    <h5 class="mb-4">
                        <i class="fas fa-chart-line me-2"></i>
                        ملخص النظام
                    </h5>

                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h6 class="text-primary">البائعين</h6>
                                <h4><?php echo $stats['sellers']['total'] ?? 0; ?></h4>
                                <small class="text-muted">مسجل</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h6 class="text-info">المنتجات</h6>
                                <h4><?php echo $stats['products']['total'] ?? 0; ?></h4>
                                <small class="text-muted">منتج</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h6 class="text-success">الطلبات</h6>
                                <h4><?php echo $stats['orders']['total'] ?? 0; ?></h4>
                                <small class="text-muted">طلب</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <h6 class="text-warning">المبيعات</h6>
                                <h4><?php echo formatPrice($stats['orders']['total_sales'] ?? 0); ?></h4>
                                <small class="text-muted">إجمالي</small>
                            </div>
                        </div>
                    </div>

                    <div class="quick-actions">
                        <h6 class="mb-3">إجراءات سريعة</h6>
                        <a href="orders.php?status=pending" class="quick-action-btn">
                            <i class="fas fa-clock me-2"></i>مراجعة الطلبات المعلقة
                        </a>
                        <a href="orders.php?status=confirmed" class="quick-action-btn">
                            <i class="fas fa-check me-2"></i>الطلبات المؤكدة
                        </a>
                        <a href="users.php" class="quick-action-btn">
                            <i class="fas fa-users me-2"></i>إدارة البائعين
                        </a>
                        <a href="products.php" class="quick-action-btn">
                            <i class="fas fa-box me-2"></i>إدارة المنتجات
                        </a>
                    </div>
                </div>
            </div>
        </div>



<?php
// تضمين footer المدير
include 'includes/admin_footer.php';
?>
