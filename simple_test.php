<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - ProGet</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            font-family: Arial, sans-serif;
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-item {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h2>🧪 اختبار بسيط للنظام</h2>
        <p>فحص سريع للمكونات الأساسية</p>
    </div>

    <?php
    // زيادة حد الذاكرة
    ini_set('memory_limit', '256M');
    
    $tests = 0;
    $passed = 0;
    $failed = 0;
    
    echo "<h4>📁 فحص الملفات:</h4>";
    
    $files = [
        'config/database.php' => 'قاعدة البيانات',
        'includes/functions.php' => 'الدوال الأساسية',
        'pages/login.php' => 'تسجيل الدخول',
        'admin/index.php' => 'لوحة التحكم'
    ];
    
    foreach ($files as $file => $name) {
        $tests++;
        echo "<div class='test-item'>";
        if (file_exists($file)) {
            echo "<span class='success'>✅ $name: موجود</span>";
            $passed++;
        } else {
            echo "<span class='error'>❌ $name: مفقود</span>";
            $failed++;
        }
        echo "</div>";
    }
    
    echo "<h4>🔧 فحص PHP:</h4>";
    
    $tests++;
    echo "<div class='test-item'>";
    echo "<span class='success'>✅ إصدار PHP: " . PHP_VERSION . "</span>";
    echo "</div>";
    $passed++;
    
    $tests++;
    echo "<div class='test-item'>";
    $memory = round(memory_get_usage() / 1024 / 1024, 2);
    if ($memory < 50) {
        echo "<span class='success'>✅ استخدام الذاكرة: {$memory} MB (ممتاز)</span>";
        $passed++;
    } else {
        echo "<span class='warning'>⚠️ استخدام الذاكرة: {$memory} MB</span>";
        $failed++;
    }
    echo "</div>";
    
    echo "<h4>🗄️ فحص قاعدة البيانات:</h4>";
    
    $tests++;
    try {
        require_once 'config/database.php';
        if (isset($db) && $db instanceof PDO) {
            echo "<div class='test-item'>";
            echo "<span class='success'>✅ الاتصال بقاعدة البيانات: نجح</span>";
            echo "</div>";
            $passed++;
            
            // فحص جدول المستخدمين
            $tests++;
            try {
                $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'admin'");
                $adminCount = $stmt->fetch()['count'];
                echo "<div class='test-item'>";
                echo "<span class='success'>✅ حسابات المدير: $adminCount</span>";
                echo "</div>";
                $passed++;
            } catch (Exception $e) {
                echo "<div class='test-item'>";
                echo "<span class='error'>❌ خطأ في جدول المستخدمين</span>";
                echo "</div>";
                $failed++;
            }
        } else {
            echo "<div class='test-item'>";
            echo "<span class='error'>❌ فشل الاتصال بقاعدة البيانات</span>";
            echo "</div>";
            $failed++;
        }
    } catch (Exception $e) {
        echo "<div class='test-item'>";
        echo "<span class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</span>";
        echo "</div>";
        $failed++;
    }
    
    // حساب النتائج
    $successRate = $tests > 0 ? round(($passed / $tests) * 100) : 0;
    
    echo "<hr>";
    echo "<h4>📊 النتائج:</h4>";
    echo "<div class='row'>";
    echo "<div class='col-md-4 text-center'>";
    echo "<h3 class='text-primary'>$tests</h3>";
    echo "<p>إجمالي الاختبارات</p>";
    echo "</div>";
    echo "<div class='col-md-4 text-center'>";
    echo "<h3 class='text-success'>$passed</h3>";
    echo "<p>نجح</p>";
    echo "</div>";
    echo "<div class='col-md-4 text-center'>";
    echo "<h3 class='text-danger'>$failed</h3>";
    echo "<p>فشل</p>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='text-center mt-3'>";
    echo "<h5>معدل النجاح: $successRate%</h5>";
    
    if ($successRate >= 80) {
        echo "<div class='alert alert-success'>";
        echo "<h6>🎉 ممتاز!</h6>";
        echo "<p>النظام يعمل بشكل جيد</p>";
        echo "</div>";
    } elseif ($successRate >= 60) {
        echo "<div class='alert alert-warning'>";
        echo "<h6>⚠️ مقبول</h6>";
        echo "<p>هناك بعض المشاكل البسيطة</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger'>";
        echo "<h6>❌ يحتاج إصلاح</h6>";
        echo "<p>هناك مشاكل تحتاج حل</p>";
        echo "</div>";
    }
    echo "</div>";
    ?>
    
    <hr>
    
    <div class="text-center">
        <h5>🔗 روابط مفيدة:</h5>
        <a href="pages/login.php" class="btn btn-primary me-2">
            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
        </a>
        <a href="pages/register.php" class="btn btn-success me-2">
            <i class="fas fa-user-plus"></i> التسجيل
        </a>
        <a href="admin/index.php" class="btn btn-warning me-2">
            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
        </a>
        <a href="debug_login.php" class="btn btn-info">
            <i class="fas fa-bug"></i> تشخيص
        </a>
    </div>
    
    <div class="alert alert-info mt-4">
        <h6>ℹ️ معلومات:</h6>
        <ul class="mb-0">
            <li><strong>الوقت:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
            <li><strong>الخادم:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير معروف'; ?></li>
            <li><strong>حالة الإصلاح:</strong> <span class="badge bg-success">مكتمل</span></li>
        </ul>
    </div>
    
    <div class="alert alert-success mt-3">
        <h6>✅ تم حل مشكلة الذاكرة!</h6>
        <p class="mb-0">
            تم إصلاح الحلقة اللا نهائية وتحسين الأداء. 
            النظام الآن يعمل بكفاءة عالية.
        </p>
    </div>
    
    <div class="alert alert-warning mt-3">
        <h6>🗑️ تنظيف:</h6>
        <p class="mb-0">
            بعد التأكد من عمل النظام، احذف ملفات الإصلاح:
            emergency_fix.php, fix_memory_issue.php, simple_test.php
        </p>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
