/* Custom CSS for Drop Shipping Platform */

/* RTL Support */
body {
    direction: rtl;
    text-align: right;
}

/* Typography */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 30px rgba(0,0,0,0.15);
}

/* Buttons */
.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(45deg, #1e7e34, #155724);
    transform: translateY(-2px);
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    border: none;
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(45deg, #e0a800, #d39e00);
    transform: translateY(-2px);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(45deg, #c82333, #bd2130);
    transform: translateY(-2px);
}

/* Form Controls */
.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

/* Sidebar */
.sidebar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: white;
    position: fixed;
    top: 0;
    right: 0;
    width: 250px;
    z-index: 1000;
    padding-top: 80px;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 15px 20px;
    border-radius: 10px;
    margin: 5px 10px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.2);
    color: white;
    transform: translateX(-5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar .badge {
    margin-right: auto;
}

/* Main Content */
.main-content {
    margin-right: 250px;
    padding: 30px;
    min-height: 100vh;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.stats-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stats-card.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.stats-card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.stats-card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.stats-card.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* Product Cards */
.product-card {
    transition: all 0.3s ease;
    border-radius: 15px;
    overflow: hidden;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.product-image {
    height: 200px;
    object-fit: cover;
    width: 100%;
    transition: all 0.3s ease;
}

.product-card:hover .product-image {
    transform: scale(1.05);
}

/* Badges */
.badge {
    font-size: 0.8rem;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 500;
}

.badge-status {
    font-size: 0.8rem;
    padding: 5px 10px;
    border-radius: 20px;
}

/* Tables */
.table {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
}

.table th {
    background-color: #f8f9fa;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.table td {
    border: none;
    vertical-align: middle;
    padding: 15px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

/* Alerts */
.alert {
    border: none;
    border-radius: 15px;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(45deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-info {
    background: linear-gradient(45deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    padding: 40px 0;
    margin-top: 50px;
}

.footer a {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer a:hover {
    color: white;
    transform: translateX(-2px);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: relative;
        width: 100%;
        min-height: auto;
        padding-top: 20px;
    }
    
    .main-content {
        margin-right: 0;
        padding: 15px;
    }
    
    .stats-card h3 {
        font-size: 2rem;
    }
    
    .product-image {
        height: 150px;
    }
    
    .btn {
        padding: 8px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .stats-card {
        padding: 20px;
        margin-bottom: 15px;
    }
    
    .stats-card h3 {
        font-size: 1.8rem;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

/* Print Styles */
@media print {
    .sidebar,
    .btn,
    .navbar {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0;
        padding: 0;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
