# منصة ProGet للتجارة الإلكترونية

منصة شاملة ومتقدمة للتجارة الإلكترونية مع نظام البائعين المتعددين ودعم كامل للغة العربية ونظام إدارة احترافي.

## 🚀 الميزات الرئيسية

### واجهة المدير
- **لوحة تحكم شاملة** مع إحصائيات مفصلة
- **إدارة البائعين** - تفعيل/تعطيل الحسابات ومراقبة الأداء
- **إدارة المنتجات** - إضافة وتعديل المنتجات المرشحة
- **إدارة الطلبات** - متابعة جميع الطلبات وحالاتها
- **إدارة الفواتير** - إنشاء وطباعة الفواتير للبائعين
- **إعدادات النظام** - تخصيص إعدادات العمولة والطلبات

### واجهة البائع
- **لوحة تحكم شخصية** مع إحصائيات الأداء
- **إدارة المنتجات** - اختيار المنتجات وتحديد الأسعار
- **إدارة الطلبات** - متابعة الطلبات وإدخال معلومات الشحن
- **الفواتير** - عرض الفواتير والمدفوعات
- **الملف الشخصي** - تعديل المعلومات الشخصية والتجارية

### الأمان والحماية
- **نظام صلاحيات محكم** (Role-Based Access Control)
- **حماية مجلد الإدارة** بـ .htaccess
- **تشفير كلمات المرور** بـ bcrypt
- **التحقق من ملكية البيانات** لكل مستخدم
- **حماية من SQL Injection** باستخدام Prepared Statements

### دعم اللغة العربية
- **واجهات RTL كاملة** مع تصميم متجاوب
- **نصوص عربية** في جميع أجزاء النظام
- **تنسيق التواريخ والأرقام** حسب المعايير العربية
- **دعم الخطوط العربية** مع تحسين القراءة

## 📋 متطلبات النظام

- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث  
- **Apache/Nginx**: مع mod_rewrite
- **Bootstrap**: 5.1.3
- **Font Awesome**: 6.0.0
- **jQuery**: للتفاعلات الديناميكية

## 🛠️ التثبيت والإعداد

### 1. تحضير البيئة
```bash
# استنساخ المشروع
git clone [repository-url]
cd proget-platform

# إعداد الصلاحيات
chmod 755 -R .
chmod 644 config/database.php
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE proget_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استيراد الهيكل
mysql -u username -p proget_db < database/proget_database.sql
```

### 3. تحديث الإعدادات
```php
// في config/database.php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'proget_db');
```

### 4. رفع الملفات
- رفع جميع الملفات إلى مجلد الويب
- التأكد من صلاحيات الكتابة للمجلدات المطلوبة

## 🔐 بيانات الدخول الافتراضية

### المدير
- **اسم المستخدم**: admin
- **البريد الإلكتروني**: <EMAIL>  
- **كلمة المرور**: password (يُنصح بتغييرها فوراً)

## 🌐 الوصول للنظام

- **واجهة المدير**: `http://yoursite.com/admin/`
- **واجهة البائع**: `http://yoursite.com/pages/`
- **تسجيل بائع جديد**: `http://yoursite.com/pages/register.php`
- **تسجيل الدخول**: `http://yoursite.com/pages/login.php`

## 📁 هيكل المشروع

```
proget-platform/
├── admin/                  # واجهة المدير
│   ├── index.php          # لوحة تحكم المدير
│   ├── sellers.php        # إدارة البائعين
│   ├── products.php       # إدارة المنتجات
│   ├── orders.php         # إدارة الطلبات
│   ├── invoices.php       # إدارة الفواتير
│   ├── invoice-details.php # تفاصيل الفاتورة
│   ├── print-invoice.php  # طباعة الفاتورة
│   ├── settings.php       # إعدادات النظام
│   └── .htaccess          # حماية مجلد الإدارة
├── pages/                 # واجهة البائع
│   ├── dashboard.php      # لوحة تحكم البائع
│   ├── products.php       # المنتجات المتاحة
│   ├── my-products.php    # منتجات البائع
│   ├── orders.php         # طلبات البائع
│   ├── invoices.php       # فواتير البائع
│   ├── profile.php        # الملف الشخصي
│   ├── login.php          # تسجيل الدخول
│   ├── register.php       # تسجيل بائع جديد
│   └── logout.php         # تسجيل الخروج
├── includes/              # الملفات المشتركة
│   ├── functions.php      # الوظائف العامة
│   ├── header.php         # رأس الصفحة
│   └── footer.php         # تذييل الصفحة
├── config/                # إعدادات النظام
│   └── database.php       # إعدادات قاعدة البيانات
├── sql/                   # قاعدة البيانات
│   └── database_structure.sql # هيكل قاعدة البيانات
├── assets/                # الملفات الثابتة
│   ├── css/              # ملفات التنسيق
│   ├── js/               # ملفات JavaScript
│   └── images/           # الصور
├── .htaccess             # إعدادات Apache
└── README.md             # دليل المستخدم
```

## 🔧 الوظائف الرئيسية

### للمدير
1. **إدارة البائعين**: تفعيل/تعطيل الحسابات، مراجعة الطلبات
2. **إدارة المنتجات**: إضافة منتجات جديدة، تحديث الأسعار
3. **متابعة الطلبات**: مراقبة جميع الطلبات وحالاتها
4. **إدارة الفواتير**: إنشاء فواتير للبائعين، تتبع المدفوعات
5. **الإحصائيات**: تقارير مفصلة عن الأداء والمبيعات

### للبائع
1. **اختيار المنتجات**: من كتالوج المنتجات المتاحة
2. **تحديد الأسعار**: وضع أسعار البيع وحساب الأرباح
3. **إدارة الطلبات**: متابعة الطلبات وتحديث حالة الشحن
4. **تتبع الأرباح**: عرض الفواتير والمدفوعات
5. **إدارة الملف الشخصي**: تحديث المعلومات التجارية

## 🛡️ الأمان

- **تشفير كلمات المرور** باستخدام bcrypt
- **حماية من SQL Injection** بـ Prepared Statements
- **نظام صلاحيات متقدم** لفصل الأدوار
- **حماية الملفات الحساسة** بـ .htaccess
- **التحقق من الجلسات** وانتهاء الصلاحية

## 📞 الدعم والمساعدة

- **البريد الإلكتروني**: <EMAIL>
- **الدعم الفني**: متوفر 24/7
- **التوثيق**: دليل مستخدم شامل
- **التحديثات**: تحديثات دورية للأمان والميزات

## 📄 الترخيص

هذا المشروع مرخص للاستخدام التجاري والشخصي.

---

**تم تطوير هذه المنصة بعناية فائقة لتوفير تجربة مستخدم متميزة ونظام إدارة احترافي للتجارة الإلكترونية.**
