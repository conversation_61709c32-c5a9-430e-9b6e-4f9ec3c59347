<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isUserLoggedIn()) {
    redirect('../pages/login.php');
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect('../pages/dashboard.php');
}

$pageTitle = 'إدارة الفواتير';

// الحصول على الإحصائيات
$stats = getAdminStats();

$message = '';
$messageType = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create') {
        // إنشاء فاتورة جديدة
        $sellerId = (int)($_POST['seller_id'] ?? 0);
        $amount = (float)($_POST['amount'] ?? 0);
        $type = $_POST['type'] ?? 'commission';
        $description = sanitizeInput($_POST['description'] ?? '');
        $dueDate = $_POST['due_date'] ?? null;
        
        if ($sellerId > 0 && $amount > 0) {
            try {
                $invoiceNumber = generateInvoiceNumber();
                
                $stmt = $db->prepare("
                    INSERT INTO invoices (invoice_number, seller_id, amount, type, description, due_date, created_by, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $stmt->execute([
                    $invoiceNumber, $sellerId, $amount, $type, $description, $dueDate, $user['id']
                ]);
                
                $message = 'تم إنشاء الفاتورة بنجاح - رقم الفاتورة: ' . $invoiceNumber;
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في إنشاء الفاتورة';
                $messageType = 'error';
            }
        } else {
            $message = 'يرجى ملء جميع الحقول المطلوبة';
            $messageType = 'error';
        }
    } elseif ($action === 'update_status') {
        $invoiceId = (int)($_POST['invoice_id'] ?? 0);
        $newStatus = $_POST['new_status'] ?? '';
        
        if ($invoiceId > 0 && in_array($newStatus, ['pending', 'paid', 'cancelled'])) {
            try {
                $stmt = $db->prepare("UPDATE invoices SET status = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$newStatus, $invoiceId]);
                $message = 'تم تحديث حالة الفاتورة بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في تحديث حالة الفاتورة';
                $messageType = 'error';
            }
        }
    }
}

// فلترة الفواتير
$statusFilter = $_GET['status'] ?? '';
$typeFilter = $_GET['type'] ?? '';
$sellerFilter = $_GET['seller'] ?? '';
$searchQuery = $_GET['search'] ?? '';

$whereConditions = [];
$params = [];

if ($statusFilter) {
    $whereConditions[] = "i.status = ?";
    $params[] = $statusFilter;
}

if ($typeFilter) {
    $whereConditions[] = "i.type = ?";
    $params[] = $typeFilter;
}

if ($sellerFilter) {
    $whereConditions[] = "i.seller_id = ?";
    $params[] = $sellerFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(i.invoice_number LIKE ? OR i.description LIKE ? OR u.full_name LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// جلب الفواتير
try {
    $stmt = $db->prepare("
        SELECT i.*, u.full_name as seller_name, u.username as seller_username,
               admin.full_name as created_by_name
        FROM invoices i 
        JOIN users u ON i.seller_id = u.id 
        JOIN users admin ON i.created_by = admin.id
        $whereClause 
        ORDER BY i.created_at DESC
    ");
    $stmt->execute($params);
    $invoices = $stmt->fetchAll();
} catch (PDOException $e) {
    $invoices = [];
}

// جلب قائمة البائعين
try {
    $stmt = $db->prepare("SELECT id, full_name, username FROM users WHERE role = 'seller' AND status = 'active' ORDER BY full_name");
    $stmt->execute();
    $sellers = $stmt->fetchAll();
} catch (PDOException $e) {
    $sellers = [];
}

// CSS إضافي خاص بهذه الصفحة
$additional_css = '
/* تخصيصات إضافية خاصة بصفحة إدارة الفواتير */
.invoices-table {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.invoice-status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.filter-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
}

.invoice-actions .btn {
    margin: 2px;
}

.overdue-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
';

// JavaScript إضافي خاص بهذه الصفحة
$additional_js = '
function viewInvoice(invoiceId) {
    window.open("invoice-details.php?id=" + invoiceId, "_blank", "width=800,height=600");
}

function printInvoice(invoiceId) {
    window.open("print-invoice.php?id=" + invoiceId, "_blank", "width=800,height=600");
}
';

// تضمين header المدير
include 'includes/admin_header.php';
?>

        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="page-title">إدارة الفواتير</h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInvoiceModal">
                <i class="fas fa-plus me-2"></i>إنشاء فاتورة جديدة
            </button>
        </div>

            <?php if ($message): ?>
                <?php echo showMessage($message, $messageType); ?>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="رقم الفاتورة أو الوصف">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                <option value="paid" <?php echo $statusFilter === 'paid' ? 'selected' : ''; ?>>مدفوعة</option>
                                <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>ملغاة</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="type" class="form-label">النوع</label>
                            <select class="form-control" id="type" name="type">
                                <option value="">جميع الأنواع</option>
                                <option value="commission" <?php echo $typeFilter === 'commission' ? 'selected' : ''; ?>>عمولة</option>
                                <option value="payout" <?php echo $typeFilter === 'payout' ? 'selected' : ''; ?>>دفعة</option>
                                <option value="other" <?php echo $typeFilter === 'other' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="seller" class="form-label">البائع</label>
                            <select class="form-control" id="seller" name="seller">
                                <option value="">جميع البائعين</option>
                                <?php foreach ($sellers as $seller): ?>
                                    <option value="<?php echo $seller['id']; ?>" 
                                            <?php echo $sellerFilter == $seller['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($seller['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="invoices.php" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-times me-1"></i>مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Invoices Table -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-file-invoice me-2"></i>قائمة الفواتير (<?php echo count($invoices); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($invoices)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>البائع</th>
                                        <th>المبلغ</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإصدار</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($invoices as $invoice): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                                <?php if ($invoice['description']): ?>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars(substr($invoice['description'], 0, 50)) . '...'; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <strong><?php echo htmlspecialchars($invoice['seller_name']); ?></strong>
                                                <br><small class="text-muted">@<?php echo htmlspecialchars($invoice['seller_username']); ?></small>
                                            </td>
                                            <td>
                                                <strong class="text-primary"><?php echo formatPrice($invoice['amount']); ?></strong>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $invoice['type'] === 'commission' ? 'info' : 
                                                        ($invoice['type'] === 'payout' ? 'success' : 'secondary'); 
                                                ?>">
                                                    <?php echo getInvoiceTypeText($invoice['type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $invoice['status'] === 'paid' ? 'success' : 
                                                        ($invoice['status'] === 'pending' ? 'warning' : 'danger'); 
                                                ?>">
                                                    <?php echo getInvoiceStatusText($invoice['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php echo formatDate($invoice['issue_date']); ?>
                                                <br><small class="text-muted">بواسطة: <?php echo htmlspecialchars($invoice['created_by_name']); ?></small>
                                            </td>
                                            <td>
                                                <?php if ($invoice['due_date']): ?>
                                                    <?php echo formatDate($invoice['due_date']); ?>
                                                    <?php if (strtotime($invoice['due_date']) < time() && $invoice['status'] === 'pending'): ?>
                                                        <br><span class="badge bg-danger">متأخر</span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">غير محدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                                            onclick="viewInvoice(<?php echo $invoice['id']; ?>)">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                            onclick="printInvoice(<?php echo $invoice['id']; ?>)">
                                                        <i class="fas fa-print"></i>
                                                    </button>
                                                    
                                                    <?php if ($invoice['status'] !== 'paid'): ?>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle" 
                                                                    data-bs-toggle="dropdown">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <?php if ($invoice['status'] !== 'paid'): ?>
                                                                    <li>
                                                                        <form method="POST" style="display: inline;">
                                                                            <input type="hidden" name="action" value="update_status">
                                                                            <input type="hidden" name="invoice_id" value="<?php echo $invoice['id']; ?>">
                                                                            <input type="hidden" name="new_status" value="paid">
                                                                            <button type="submit" class="dropdown-item">
                                                                                تحديد كمدفوعة
                                                                            </button>
                                                                        </form>
                                                                    </li>
                                                                <?php endif; ?>
                                                                <?php if ($invoice['status'] !== 'cancelled'): ?>
                                                                    <li>
                                                                        <form method="POST" style="display: inline;">
                                                                            <input type="hidden" name="action" value="update_status">
                                                                            <input type="hidden" name="invoice_id" value="<?php echo $invoice['id']; ?>">
                                                                            <input type="hidden" name="new_status" value="cancelled">
                                                                            <button type="submit" class="dropdown-item" 
                                                                                    onclick="return confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟')">
                                                                                إلغاء الفاتورة
                                                                            </button>
                                                                        </form>
                                                                    </li>
                                                                <?php endif; ?>
                                                            </ul>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد فواتير</h5>
                            <p class="text-muted">لم يتم العثور على فواتير بالمعايير المحددة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

<!-- Create Invoice Modal -->
<div class="modal fade" id="createInvoiceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء فاتورة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="seller_id" class="form-label">البائع *</label>
                                <select class="form-control" id="seller_id" name="seller_id" required>
                                    <option value="">اختر البائع</option>
                                    <?php foreach ($sellers as $seller): ?>
                                        <option value="<?php echo $seller['id']; ?>">
                                            <?php echo htmlspecialchars($seller['full_name']); ?> (@<?php echo htmlspecialchars($seller['username']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="amount" class="form-label">المبلغ *</label>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">نوع الفاتورة</label>
                                <select class="form-control" id="type" name="type">
                                    <option value="commission">عمولة</option>
                                    <option value="payout">دفعة</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                <input type="date" class="form-control" id="due_date" name="due_date">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3" 
                                  placeholder="وصف الفاتورة أو تفاصيل إضافية"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إنشاء الفاتورة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// تضمين footer المدير
include 'includes/admin_footer.php';
?>
