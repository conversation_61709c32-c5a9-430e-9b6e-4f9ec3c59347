<?php
require_once 'includes/functions.php';

$pageTitle = 'الرئيسية';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - منصة البائعين</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">

    <style>
        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Navbar styling */
        .navbar {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-nav .nav-link {
            transition: all 0.3s ease;
            position: relative;
            font-weight: 500;
        }

        .navbar-nav .nav-link:hover {
            color: #ffffff !important;
            transform: translateY(-2px);
        }

        .navbar-nav .nav-link.active {
            color: #ffffff !important;
        }

        .navbar-nav .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 50%;
            background: #ffffff;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .navbar-nav .nav-link:hover::after,
        .navbar-nav .nav-link.active::after {
            width: 80%;
        }

        /* Hero Section with animated background */
        .hero-section {
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 1;
        }

        .hero-section .container {
            position: relative;
            z-index: 2;
        }

        /* Animated shapes in hero background */
        .hero-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            left: 80%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 80%;
            left: 20%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            width: 100px;
            height: 100px;
            top: 10%;
            left: 70%;
            animation-delay: 1s;
        }

        .shape:nth-child(5) {
            width: 40px;
            height: 40px;
            top: 40%;
            left: 5%;
            animation-delay: 3s;
        }

        .shape:nth-child(6) {
            width: 90px;
            height: 90px;
            top: 70%;
            left: 60%;
            animation-delay: 5s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        /* Floating particles */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: particle-float 8s linear infinite;
        }

        .particle:nth-child(odd) {
            animation-duration: 10s;
            background: rgba(255, 255, 255, 0.6);
        }

        @keyframes particle-float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        /* Service cards hover effect */
        .service-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
        }

        /* Contact form styling */
        .contact-item {
            transition: all 0.3s ease;
        }

        .contact-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
        }

        /* Social links */
        .social-links .btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Section spacing */
        section {
            scroll-margin-top: 70px;
        }

        /* Statistics Section */
        .stats-item {
            transition: all 0.3s ease;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .stats-item:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
        }

        .stats-icon {
            transition: transform 0.3s ease;
        }

        .stats-item:hover .stats-icon {
            transform: scale(1.1);
        }

        .counter {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
        }

        /* Hero content animation */
        .hero-content {
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Hero image animation */
        .hero-image {
            animation: fadeInRight 1s ease-out 0.3s both;
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>

<!-- Navigation Bar -->
<nav class="navbar navbar-expand-lg navbar-dark fixed-top" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <a class="navbar-brand" href="#home">
            <i class="fas fa-store me-2"></i>منصة البائعين
        </a>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="#home">الرئيسية</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#services">خدماتنا</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#how-it-works">كيف تعمل منصتنا</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#contact">اتصل بنا</a>
                </li>
            </ul>

            <ul class="navbar-nav">
                <?php if (!isUserLoggedIn()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/login.php">تسجيل الدخول</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-primary text-white ms-2 px-3" href="pages/register.php">التسجيل</a>
                    </li>
                <?php else: ?>
                    <?php
                    $currentUser = getCurrentUser();
                    if ($currentUser && $currentUser['status'] === 'active'):
                    ?>
                        <li class="nav-item">
                            <a class="nav-link" href="pages/dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="pages/logout.php">تسجيل الخروج</a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="pages/account-status.php">حالة الحساب</a>
                        </li>
                    <?php endif; ?>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>

<!-- Success/Info Messages -->
<?php if (isset($_GET['logout']) && $_GET['logout'] === 'success'): ?>
<div class="alert alert-success alert-dismissible fade show m-0" role="alert" style="margin-top: 56px !important; border-radius: 0;">
    <div class="container">
        <i class="fas fa-check-circle me-2"></i>
        تم تسجيل الخروج بنجاح. نراك قريباً!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
</div>
<?php endif; ?>

<!-- Hero Section -->
<section id="home" class="hero-section py-5" style="color: white; margin-top: <?php echo isset($_GET['logout']) ? '0' : '56px'; ?>; min-height: 100vh; display: flex; align-items: center;">
    <!-- Animated Background Shapes -->
    <div class="hero-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Floating Particles -->
    <div class="particles">
        <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
        <div class="particle" style="left: 20%; animation-delay: 1s;"></div>
        <div class="particle" style="left: 30%; animation-delay: 2s;"></div>
        <div class="particle" style="left: 40%; animation-delay: 3s;"></div>
        <div class="particle" style="left: 50%; animation-delay: 4s;"></div>
        <div class="particle" style="left: 60%; animation-delay: 5s;"></div>
        <div class="particle" style="left: 70%; animation-delay: 6s;"></div>
        <div class="particle" style="left: 80%; animation-delay: 7s;"></div>
        <div class="particle" style="left: 90%; animation-delay: 8s;"></div>
        <div class="particle" style="left: 15%; animation-delay: 2.5s;"></div>
        <div class="particle" style="left: 35%; animation-delay: 4.5s;"></div>
        <div class="particle" style="left: 55%; animation-delay: 6.5s;"></div>
        <div class="particle" style="left: 75%; animation-delay: 8.5s;"></div>
    </div>

    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 hero-content">
                <h1 class="display-4 fw-bold mb-4">ابدأ رحلتك في التجارة الإلكترونية</h1>
                <p class="lead mb-4">
                    انضم إلى منصتنا المتطورة للبيع بنموذج Drop Shipping.
                    اختر من مجموعة واسعة من المنتجات عالية الجودة وابدأ البيع فوراً بدون الحاجة لمخزون.
                </p>
                <div class="d-flex gap-3 flex-wrap">
                    <?php if (!isUserLoggedIn()): ?>
                        <a href="pages/register.php" class="btn btn-light btn-lg px-4">
                            <i class="fas fa-user-plus me-2"></i>سجل الآن
                        </a>
                        <a href="pages/login.php" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    <?php else: ?>
                        <?php
                        $currentUser = getCurrentUser();
                        if ($currentUser && $currentUser['status'] === 'active'):
                        ?>
                            <a href="pages/dashboard.php" class="btn btn-light btn-lg px-4">
                                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                            </a>
                            <a href="pages/my-products.php" class="btn btn-outline-light btn-lg px-4">
                                <i class="fas fa-box me-2"></i>منتجاتي
                            </a>
                        <?php else: ?>
                            <a href="pages/account-status.php" class="btn btn-light btn-lg px-4">
                                <i class="fas fa-user-clock me-2"></i>حالة الحساب
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="col-lg-6 text-center hero-image">
                <img src="https://via.placeholder.com/500x400?text=Drop+Shipping+Platform"
                     alt="منصة Drop Shipping" class="img-fluid rounded shadow-lg">
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">لماذا تختار منصتنا؟</h2>
            <p class="text-muted">نوفر لك جميع الأدوات والخدمات التي تحتاجها لنجاح أعمالك</p>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-boxes fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">منتجات متنوعة</h5>
                        <p class="card-text">
                            مجموعة واسعة من المنتجات عالية الجودة من موردين موثوقين
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-shipping-fast fa-3x text-success"></i>
                        </div>
                        <h5 class="card-title">شحن سريع</h5>
                        <p class="card-text">
                            نظام شحن متطور يضمن وصول المنتجات للعملاء في أسرع وقت
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-chart-line fa-3x text-warning"></i>
                        </div>
                        <h5 class="card-title">أرباح مضمونة</h5>
                        <p class="card-text">
                            نظام عمولة شفاف وأرباح مجزية مع إمكانية تتبع المبيعات
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-headset fa-3x text-info"></i>
                        </div>
                        <h5 class="card-title">دعم فني 24/7</h5>
                        <p class="card-text">
                            فريق دعم متخصص متاح على مدار الساعة لمساعدتك
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-mobile-alt fa-3x text-danger"></i>
                        </div>
                        <h5 class="card-title">منصة متجاوبة</h5>
                        <p class="card-text">
                            تصميم متجاوب يعمل بكفاءة على جميع الأجهزة
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card h-100 text-center border-0 shadow-sm">
                    <div class="card-body p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-shield-alt fa-3x text-secondary"></i>
                        </div>
                        <h5 class="card-title">أمان عالي</h5>
                        <p class="card-text">
                            حماية متقدمة لبياناتك ومعاملاتك المالية
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section id="services" class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">خدماتنا</h2>
            <p class="text-muted">نقدم مجموعة شاملة من الخدمات لضمان نجاح أعمالك</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="service-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fas fa-warehouse fa-3x text-primary"></i>
                        </div>
                        <h5 class="card-title">إدارة المخزون</h5>
                        <p class="card-text">
                            نحن نتولى إدارة المخزون بالكامل، لا تحتاج للقلق بشأن التخزين أو الشحن
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fas fa-credit-card fa-3x text-success"></i>
                        </div>
                        <h5 class="card-title">معالجة المدفوعات</h5>
                        <p class="card-text">
                            نظام دفع آمن ومتطور يدعم جميع وسائل الدفع المحلية والعالمية
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fas fa-chart-bar fa-3x text-warning"></i>
                        </div>
                        <h5 class="card-title">تقارير مفصلة</h5>
                        <p class="card-text">
                            احصل على تقارير شاملة عن مبيعاتك وأرباحك مع إحصائيات مفصلة
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fas fa-users fa-3x text-info"></i>
                        </div>
                        <h5 class="card-title">إدارة العملاء</h5>
                        <p class="card-text">
                            نظام متطور لإدارة العملاء وتتبع طلباتهم وتقديم أفضل خدمة
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fas fa-truck fa-3x text-danger"></i>
                        </div>
                        <h5 class="card-title">خدمات الشحن</h5>
                        <p class="card-text">
                            شراكات مع أفضل شركات الشحن لضمان وصول المنتجات بأمان وسرعة
                        </p>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="service-card card h-100 border-0 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="service-icon mb-3">
                            <i class="fas fa-phone-alt fa-3x text-secondary"></i>
                        </div>
                        <h5 class="card-title">دعم العملاء</h5>
                        <p class="card-text">
                            فريق دعم متخصص متاح 24/7 لمساعدتك في حل أي مشكلة أو استفسار
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section id="statistics" class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">إحصائياتنا</h2>
            <p class="text-muted">أرقام تتحدث عن نجاحنا وثقة عملائنا</p>
        </div>

        <div class="row text-center">
            <div class="col-md-3 mb-4">
                <div class="stats-item p-4 bg-white rounded shadow-sm h-100">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-users fa-3x text-primary"></i>
                    </div>
                    <h3 class="display-4 fw-bold text-primary counter" data-target="500">0</h3>
                    <span class="text-primary fs-4 fw-bold">+</span>
                    <p class="text-muted mt-2 mb-0">بائع نشط</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-item p-4 bg-white rounded shadow-sm h-100">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-boxes fa-3x text-success"></i>
                    </div>
                    <h3 class="display-4 fw-bold text-success counter" data-target="1000">0</h3>
                    <span class="text-success fs-4 fw-bold">+</span>
                    <p class="text-muted mt-2 mb-0">منتج متاح</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-item p-4 bg-white rounded shadow-sm h-100">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-shopping-cart fa-3x text-warning"></i>
                    </div>
                    <h3 class="display-4 fw-bold text-warning counter" data-target="5000">0</h3>
                    <span class="text-warning fs-4 fw-bold">+</span>
                    <p class="text-muted mt-2 mb-0">طلب مكتمل</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-item p-4 bg-white rounded shadow-sm h-100">
                    <div class="stats-icon mb-3">
                        <i class="fas fa-heart fa-3x text-info"></i>
                    </div>
                    <h3 class="display-4 fw-bold text-info counter" data-target="98">0</h3>
                    <span class="text-info fs-4 fw-bold">%</span>
                    <p class="text-muted mt-2 mb-0">رضا العملاء</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- How it Works Section -->
<section id="how-it-works" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">كيف تعمل منصتنا؟</h2>
            <p class="text-muted">خطوات بسيطة للبدء في رحلتك التجارية معنا</p>
        </div>
        
        <div class="row g-4">
            <div class="col-md-3 text-center">
                <div class="step-item">
                    <div class="step-number bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">1</div>
                    <h5>سجل حسابك</h5>
                    <p class="text-muted">أنشئ حساب بائع جديد وأكمل بياناتك</p>
                </div>
            </div>
            
            <div class="col-md-3 text-center">
                <div class="step-item">
                    <div class="step-number bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">2</div>
                    <h5>اختر المنتجات</h5>
                    <p class="text-muted">تصفح واختر المنتجات التي تريد بيعها</p>
                </div>
            </div>
            
            <div class="col-md-3 text-center">
                <div class="step-item">
                    <div class="step-number bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">3</div>
                    <h5>ابدأ البيع</h5>
                    <p class="text-muted">روج لمنتجاتك واستقبل الطلبات</p>
                </div>
            </div>
            
            <div class="col-md-3 text-center">
                <div class="step-item">
                    <div class="step-number bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 60px; height: 60px; font-size: 1.5rem; font-weight: bold;">4</div>
                    <h5>احصل على الأرباح</h5>
                    <p class="text-muted">تتبع مبيعاتك واحصل على عمولتك</p>
                </div>
            </div>
        </div>
    </div>
</section>



<!--  CTA Section-->
<section class="py-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
    <div class="container text-center">
        <h2 class="fw-bold mb-4">هل أنت مستعد للبدء؟</h2>
        <p class="lead mb-4">انضم إلى آلاف البائعين الناجحين على منصتنا</p>
        <?php if (!isSellerLoggedIn()): ?>
            <a href="pages/register.php" class="btn btn-light btn-lg px-5">
                <i class="fas fa-rocket me-2"></i>ابدأ الآن مجاناً
            </a>
        <?php else: ?>
            <a href="pages/dashboard.php" class="btn btn-light btn-lg px-5">
                <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
            </a>
        <?php endif; ?>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">اتصل بنا</h2>
            <p class="text-muted">نحن هنا لمساعدتك في أي وقت</p>
        </div>

        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-4">
                        <h5 class="card-title mb-4">معلومات التواصل</h5>

                        <div class="contact-info">
                            <div class="contact-item d-flex align-items-center mb-3">
                                <div class="contact-icon me-3">
                                    <i class="fab fa-whatsapp fa-lg text-success"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">واتساب</h6>
                                    <p class="text-muted mb-0">
                                        <a href="https://wa.me/212612345678" target="_blank" class="text-success text-decoration-none">
                                            +212 6 12 34 56 78
                                        </a>
                                    </p>
                                </div>
                            </div>

                            <div class="contact-item d-flex align-items-center mb-3">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-envelope fa-lg text-info"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">البريد الإلكتروني</h6>
                                    <p class="text-muted mb-0"><EMAIL></p>
                                </div>
                            </div>

                            <div class="contact-item d-flex align-items-center mb-3">
                                <div class="contact-icon me-3">
                                    <i class="fas fa-clock fa-lg text-warning"></i>
                                </div>
                                <div>
                                    <h6 class="mb-1">ساعات العمل</h6>
                                    <p class="text-muted mb-0">الاثنين - الجمعة: 9:00 ص - 6:00 م</p>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="social-links text-center">
                            <h6 class="mb-3">تابعنا على</h6>
                            <div class="d-flex justify-content-center gap-3">
                                <a href="https://wa.me/212612345678" target="_blank" class="btn btn-outline-success btn-sm" title="واتساب">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                                <a href="#" class="btn btn-outline-primary btn-sm" title="فيسبوك">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="#" class="btn btn-outline-danger btn-sm" title="إنستغرام">
                                    <i class="fab fa-instagram"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="footer text-white py-5 mt-5" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <h5><i class="fas fa-store me-2"></i>منصة البائعين</h5>
                <p>منصة متكاملة لإدارة أعمال البيع بنموذج Drop Shipping. ابدأ رحلتك التجارية معنا اليوم!</p>
                <div class="social-links">
                    <a href="https://wa.me/212612345678" target="_blank" class="text-white me-3" title="واتساب">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                    <a href="#" class="text-white me-3" title="فيسبوك">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" class="text-white me-3" title="إنستغرام">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>

            <div class="col-md-2">
                <h6>روابط سريعة</h6>
                <ul class="list-unstyled">
                    <li><a href="#home" class="text-white-50">الرئيسية</a></li>
                    <li><a href="#services" class="text-white-50">خدماتنا</a></li>
                    <li><a href="#how-it-works" class="text-white-50">كيف تعمل منصتنا</a></li>
                    <li><a href="#contact" class="text-white-50">اتصل بنا</a></li>
                </ul>
            </div>

            <div class="col-md-2">
                <h6>للبائعين</h6>
                <ul class="list-unstyled">
                    <li><a href="pages/register.php" class="text-white-50">التسجيل</a></li>
                    <li><a href="pages/login.php" class="text-white-50">تسجيل الدخول</a></li>
                    <li><a href="pages/seller-guide.php" class="text-white-50">دليل البائع</a></li>
                </ul>
            </div>

            <div class="col-md-2">
                <h6>الدعم</h6>
                <ul class="list-unstyled">
                    <li><a href="pages/terms.php" class="text-white-50">الشروط والأحكام</a></li>
                    <li><a href="pages/privacy.php" class="text-white-50">سياسة الخصوصية</a></li>
                    <li><a href="pages/refund.php" class="text-white-50">سياسة الاسترداد</a></li>
                </ul>
            </div>

            <div class="col-md-2">
                <h6>تواصل معنا</h6>
                <ul class="list-unstyled text-white-50">
                    <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                    <li><i class="fab fa-whatsapp me-2"></i>+212 6 12 34 56 78</li>
                </ul>
            </div>
        </div>

        <hr class="my-4">

        <div class="row align-items-center">
            <div class="col-md-12 text-center">
                <p class="mb-0 text-white">
                    &copy; <?php echo date('Y'); ?> منصة البائعين. جميع الحقوق محفوظة.
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom JavaScript -->
<script src="assets/js/main.js"></script>

<script>
// Navbar scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'linear-gradient(135deg, rgba(102, 126, 234, 0.95) 0%, rgba(118, 75, 162, 0.95) 100%)';
        navbar.style.backdropFilter = 'blur(10px)';
    } else {
        navbar.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        navbar.style.backdropFilter = 'none';
    }
});



// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Active navigation highlighting
window.addEventListener('scroll', function() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.clientHeight;
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
            link.classList.add('active');
        }
    });
});

// Add more floating particles dynamically
function createParticle() {
    const particle = document.createElement('div');
    particle.className = 'particle';
    particle.style.left = Math.random() * 100 + '%';
    particle.style.animationDelay = Math.random() * 8 + 's';
    particle.style.animationDuration = (Math.random() * 3 + 8) + 's';

    const particles = document.querySelector('.particles');
    if (particles) {
        particles.appendChild(particle);

        // Remove particle after animation
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, 12000);
    }
}

// Create particles periodically
setInterval(createParticle, 2000);

// Add scroll animations for sections
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe all sections except hero
document.querySelectorAll('section:not(#home)').forEach(section => {
    section.style.opacity = '0';
    section.style.transform = 'translateY(30px)';
    section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(section);
});

// Add hover effects to buttons
document.querySelectorAll('.btn').forEach(button => {
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-2px)';
        this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.2)';
    });

    button.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
        this.style.boxShadow = '';
    });
});



// Add typing effect to hero title
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';

    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }

    type();
}

// Initialize typing effect when page loads
window.addEventListener('load', function() {
    const heroTitle = document.querySelector('#home h1');
    if (heroTitle) {
        const originalText = heroTitle.textContent;
        setTimeout(() => {
            typeWriter(heroTitle, originalText, 80);
        }, 500);
    }
});

// Simple and working counter animation
function startCounters() {
    const counters = document.querySelectorAll('.counter');

    counters.forEach((counter, index) => {
        const target = parseInt(counter.getAttribute('data-target'));
        let current = 0;

        // Reset counter to 0 first
        counter.textContent = '0';

        // Start animation with delay for each counter
        setTimeout(() => {
            const increment = target / 50; // 50 steps for smooth animation

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.floor(current);
                    setTimeout(updateCounter, 30); // 30ms delay between updates
                } else {
                    counter.textContent = target; // Ensure final value is exact
                }
            };

            updateCounter();
        }, index * 300); // 300ms delay between each counter
    });
}

// Start animation when page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(startCounters, 1500); // Start after 1.5 seconds
});

// Also start when scrolling to statistics section
let animationTriggered = false;
window.addEventListener('scroll', function() {
    if (animationTriggered) return;

    const statsSection = document.querySelector('#statistics');
    if (statsSection) {
        const rect = statsSection.getBoundingClientRect();
        const isVisible = rect.top < window.innerHeight && rect.bottom > 0;

        if (isVisible) {
            startCounters();
            animationTriggered = true;
        }
    }
});

// Add hover effects to stats items
document.querySelectorAll('.stats-item').forEach(item => {
    item.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05)';
        this.style.transition = 'transform 0.3s ease';
    });

    item.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1)';
    });
});
</script>

</body>
</html>
