// ملف JavaScript الرئيسي للمنصة

// تهيئة المتغيرات العامة
const APP = {
    baseUrl: window.location.origin,
    currentPage: window.location.pathname,
    isRTL: document.documentElement.dir === 'rtl'
};

// وظائف مساعدة
const Utils = {
    // تنسيق الأرقام
    formatNumber: function(number, decimals = 2) {
        return new Intl.NumberFormat('ar-SA', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        }).format(number);
    },
    
    // تنسيق العملة
    formatCurrency: function(amount) {
        return new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
        }).format(amount);
    },
    
    // تنسيق التاريخ
    formatDate: function(date) {
        return new Intl.DateTimeFormat('ar-SA', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    },
    
    // عرض رسالة تأكيد
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },
    
    // إظهار/إخفاء عنصر
    toggle: function(element) {
        if (element.style.display === 'none') {
            element.style.display = 'block';
        } else {
            element.style.display = 'none';
        }
    }
};

// وظائف التفاعل مع النماذج
const Forms = {
    // التحقق من صحة البريد الإلكتروني
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    // التحقق من قوة كلمة المرور
    validatePassword: function(password) {
        return password.length >= 6;
    },
    
    // تنظيف البيانات
    sanitizeInput: function(input) {
        return input.trim().replace(/[<>]/g, '');
    },
    
    // إرسال نموذج عبر AJAX
    submitForm: function(formElement, callback) {
        const formData = new FormData(formElement);
        
        fetch(formElement.action, {
            method: formElement.method,
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (callback) callback(data);
        })
        .catch(error => {
            console.error('خطأ في إرسال النموذج:', error);
        });
    }
};

// وظائف واجهة المستخدم
const UI = {
    // إظهار رسالة تحميل
    showLoading: function(element) {
        element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        element.disabled = true;
    },
    
    // إخفاء رسالة التحميل
    hideLoading: function(element, originalText) {
        element.innerHTML = originalText;
        element.disabled = false;
    },
    
    // إظهار إشعار
    showNotification: function(message, type = 'info', duration = 5000) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    },
    
    // تحديث عداد
    updateCounter: function(element, newValue) {
        const currentValue = parseInt(element.textContent) || 0;
        const increment = newValue > currentValue ? 1 : -1;
        
        const timer = setInterval(() => {
            const current = parseInt(element.textContent) || 0;
            if (current === newValue) {
                clearInterval(timer);
            } else {
                element.textContent = current + increment;
            }
        }, 50);
    }
};

// وظائف خاصة بالمنتجات
const Products = {
    // إضافة منتج إلى قائمة البائع
    addToMyProducts: function(productId, button) {
        const originalText = button.innerHTML;
        UI.showLoading(button);
        
        fetch('ajax/add-product.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: productId
            })
        })
        .then(response => response.json())
        .then(data => {
            UI.hideLoading(button, originalText);
            
            if (data.success) {
                UI.showNotification('تم إضافة المنتج بنجاح!', 'success');
                button.innerHTML = '<i class="fas fa-check"></i> تم الإضافة';
                button.disabled = true;
                button.classList.remove('btn-primary');
                button.classList.add('btn-success');
            } else {
                UI.showNotification(data.message || 'حدث خطأ!', 'error');
            }
        })
        .catch(error => {
            UI.hideLoading(button, originalText);
            UI.showNotification('حدث خطأ في الاتصال!', 'error');
        });
    },
    
    // تحديث سعر المنتج
    updatePrice: function(productId, newPrice, callback) {
        fetch('ajax/update-price.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: productId,
                price: newPrice
            })
        })
        .then(response => response.json())
        .then(data => {
            if (callback) callback(data);
        })
        .catch(error => {
            console.error('خطأ في تحديث السعر:', error);
        });
    }
};

// تهيئة الصفحة عند التحميل
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النماذج
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // تهيئة الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(function(button) {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
    
    // تهيئة حقول الإدخال
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(function(input) {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });
    
    // إخفاء الرسائل تلقائياً
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (bootstrap && bootstrap.Alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
});

// تصدير الوظائف للاستخدام العام
window.Utils = Utils;
window.Forms = Forms;
window.UI = UI;
window.Products = Products;
