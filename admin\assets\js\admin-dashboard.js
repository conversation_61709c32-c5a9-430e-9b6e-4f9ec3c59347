/**
 * لوحة تحكم المدير - ملف JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // عناصر DOM
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    const userProfile = document.querySelector('.user-profile');
    
    // تبديل الشريط الجانبي
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            toggleSidebar();
        });
    }
    
    // دالة تبديل الشريط الجانبي
    function toggleSidebar() {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
        
        // تغيير أيقونة الزر
        const icon = sidebarToggle.querySelector('i');
        if (sidebar.classList.contains('collapsed')) {
            icon.classList.remove('fa-bars');
            icon.classList.add('fa-arrow-left');
        } else {
            icon.classList.remove('fa-arrow-left');
            icon.classList.add('fa-bars');
        }
        
        // حفظ حالة الشريط الجانبي
        localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
    }
    
    // استعادة حالة الشريط الجانبي
    function restoreSidebarState() {
        const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        if (isCollapsed) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            const icon = sidebarToggle.querySelector('i');
            icon.classList.remove('fa-bars');
            icon.classList.add('fa-arrow-left');
        }
    }
    
    // للشاشات الصغيرة
    function handleMobileView() {
        if (window.innerWidth <= 768) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            
            // إضافة مستمع للنقر على زر التبديل
            sidebarToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                sidebar.classList.toggle('show');
            });
            
            // إغلاق الشريط الجانبي عند النقر خارجه
            document.addEventListener('click', function(e) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            });
        }
    }
    
    // تفعيل الرابط النشط
    function setActiveLink() {
        const currentPage = window.location.pathname.split('/').pop();
        const menuLinks = document.querySelectorAll('.sidebar-menu a');
        
        menuLinks.forEach(link => {
            link.classList.remove('active');
            const href = link.getAttribute('href');
            if (href === currentPage || (currentPage === '' && href === 'index.php')) {
                link.classList.add('active');
            }
        });
    }
    
    // تأثيرات الحركة للبطاقات
    function animateStatsCards() {
        const cards = document.querySelectorAll('.stats-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1
        });
        
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }
    
    // تحديث الوقت الحالي
    function updateCurrentTime() {
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            };
            timeElement.textContent = now.toLocaleDateString('ar-SA', options);
        }
    }
    
    // تحديث الإحصائيات بشكل دوري
    function updateStats() {
        // يمكن إضافة AJAX لتحديث الإحصائيات
        console.log('تحديث الإحصائيات...');
    }
    
    // إضافة تأثيرات hover للبطاقات
    function addCardHoverEffects() {
        const cards = document.querySelectorAll('.stats-card');
        
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    }
    
    // إضافة tooltips للأيقونات
    function addTooltips() {
        const menuItems = document.querySelectorAll('.sidebar-menu a');
        
        menuItems.forEach(item => {
            const span = item.querySelector('span');
            if (span) {
                item.setAttribute('title', span.textContent);
            }
        });
    }
    
    // مراقبة تغيير حجم الشاشة
    function handleResize() {
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
            handleMobileView();
        });
    }
    
    // إضافة مؤثرات صوتية (اختيارية)
    function addSoundEffects() {
        const menuLinks = document.querySelectorAll('.sidebar-menu a');
        
        menuLinks.forEach(link => {
            link.addEventListener('click', function() {
                // يمكن إضافة صوت النقر هنا
                // playClickSound();
            });
        });
    }
    
    // دالة تحميل البيانات بـ AJAX
    function loadDashboardData() {
        // يمكن استخدامها لتحميل البيانات دون إعادة تحميل الصفحة
        fetch('api/dashboard-stats.php')
            .then(response => response.json())
            .then(data => {
                updateDashboardStats(data);
            })
            .catch(error => {
                console.error('خطأ في تحميل البيانات:', error);
            });
    }
    
    // تحديث إحصائيات لوحة التحكم
    function updateDashboardStats(data) {
        // تحديث أرقام الإحصائيات
        if (data.sellers) {
            const sellersElement = document.querySelector('.stats-number');
            if (sellersElement) {
                animateNumber(sellersElement, data.sellers.total);
            }
        }
    }
    
    // تحريك الأرقام
    function animateNumber(element, targetNumber) {
        const startNumber = 0;
        const duration = 1000; // مدة التحريك بالميلي ثانية
        const startTime = performance.now();
        
        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentNumber = Math.floor(startNumber + (targetNumber - startNumber) * progress);
            element.textContent = currentNumber;
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }
        
        requestAnimationFrame(updateNumber);
    }
    
    // إضافة اختصارات لوحة المفاتيح
    function addKeyboardShortcuts() {
        document.addEventListener('keydown', function(e) {
            // Ctrl + B لتبديل الشريط الجانبي
            if (e.ctrlKey && e.key === 'b') {
                e.preventDefault();
                toggleSidebar();
            }
            
            // Escape لإغلاق الشريط الجانبي في الشاشات الصغيرة
            if (e.key === 'Escape' && window.innerWidth <= 768) {
                sidebar.classList.remove('show');
            }
        });
    }
    
    // إضافة مؤشر التحميل
    function showLoadingIndicator() {
        const loader = document.createElement('div');
        loader.className = 'loading-indicator';
        loader.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
        document.body.appendChild(loader);
        
        setTimeout(() => {
            loader.remove();
        }, 2000);
    }
    
    // تهيئة جميع الوظائف
    function init() {
        restoreSidebarState();
        handleMobileView();
        setActiveLink();
        animateStatsCards();
        updateCurrentTime();
        addCardHoverEffects();
        addTooltips();
        handleResize();
        addKeyboardShortcuts();

        // تهيئة الإشعارات
        if (window.AdminDashboard && window.AdminDashboard.notifications) {
            window.AdminDashboard.notifications.init();
        }

        // تحديث الوقت كل دقيقة
        setInterval(updateCurrentTime, 60000);

        // تحديث الإحصائيات كل 5 دقائق
        setInterval(updateStats, 300000);
    }
    
    // بدء التطبيق
    init();
    
    // إضافة مستمع لتحميل الصفحة
    window.addEventListener('load', function() {
        document.body.classList.add('loaded');
    });
});

// دوال مساعدة عامة
window.AdminDashboard = {
    // إظهار رسالة نجاح
    showSuccess: function(message) {
        this.showNotification(message, 'success');
    },
    
    // إظهار رسالة خطأ
    showError: function(message) {
        this.showNotification(message, 'error');
    },
    
    // إظهار إشعار
    showNotification: function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار بعد 5 ثواني
        setTimeout(() => {
            notification.remove();
        }, 5000);
        
        // إزالة الإشعار عند النقر على زر الإغلاق
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    },
    
    // تأكيد الحذف
    confirmDelete: function(message = 'هل أنت متأكد من الحذف؟') {
        return confirm(message);
    },

    // إدارة الإشعارات
    notifications: {
        // تهيئة الإشعارات
        init: function() {
            this.bindEvents();
            this.updateCounts();
            setInterval(() => this.updateCounts(), 30000); // تحديث كل 30 ثانية
        },

        // ربط الأحداث
        bindEvents: function() {
            const ordersBtn = document.getElementById('ordersNotification');
            const usersBtn = document.getElementById('usersNotification');
            const generalBtn = document.getElementById('generalNotification');

            if (ordersBtn) {
                ordersBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDropdown('ordersDropdown');
                    this.loadNotifications('orders');
                });
            }

            if (usersBtn) {
                usersBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDropdown('usersDropdown');
                    this.loadNotifications('users');
                });
            }

            if (generalBtn) {
                generalBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleDropdown('generalDropdown');
                });
            }

            // إغلاق القوائم عند النقر خارجها
            document.addEventListener('click', () => {
                this.closeAllDropdowns();
            });
        },

        // تبديل القائمة المنسدلة
        toggleDropdown: function(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            if (dropdown) {
                // إغلاق القوائم الأخرى
                this.closeAllDropdowns();
                dropdown.classList.toggle('show');
            }
        },

        // إغلاق جميع القوائم
        closeAllDropdowns: function() {
            const dropdowns = document.querySelectorAll('.notifications-dropdown');
            dropdowns.forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        },

        // تحميل الإشعارات
        loadNotifications: function(type) {
            fetch(`api/notifications.php?type=${type}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.renderNotifications(type, data.data);
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل الإشعارات:', error);
                });
        },

        // عرض الإشعارات
        renderNotifications: function(type, notifications) {
            const containerId = type + 'NotificationsList';
            const container = document.getElementById(containerId);

            if (!container) return;

            if (notifications.length === 0) {
                container.innerHTML = '<div class="text-center p-3 text-muted">لا توجد إشعارات</div>';
                return;
            }

            container.innerHTML = notifications.map(notification => `
                <div class="notification-item ${notification.unread ? 'unread' : ''}">
                    <div class="notification-content">
                        <div class="notification-avatar ${notification.type}">
                            <i class="fas fa-${this.getIconForType(notification.type)}"></i>
                        </div>
                        <div class="notification-text">
                            <div class="notification-title">${notification.title}</div>
                            <div class="notification-desc">${notification.description}</div>
                            <div class="notification-time">${notification.time}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        },

        // الحصول على الأيقونة حسب النوع
        getIconForType: function(type) {
            const icons = {
                'order': 'shopping-cart',
                'user': 'user-plus',
                'system': 'exclamation',
                'security': 'shield-alt',
                'product': 'box'
            };
            return icons[type] || 'bell';
        },

        // تحديث العدادات
        updateCounts: function() {
            fetch('api/notifications.php?type=counts')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        this.updateBadges(data.data);
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحديث العدادات:', error);
                });
        },

        // تحديث الشارات
        updateBadges: function(counts) {
            const badges = {
                'ordersBadge': counts.orders,
                'usersBadge': counts.users,
                'generalBadge': counts.general
            };

            Object.keys(badges).forEach(badgeId => {
                const badge = document.getElementById(badgeId);
                if (badge) {
                    const count = badges[badgeId];
                    badge.textContent = count;
                    badge.classList.toggle('zero', count === 0);

                    if (count > 0) {
                        badge.style.animation = 'pulse 1s ease-in-out';
                        setTimeout(() => {
                            badge.style.animation = '';
                        }, 1000);
                    }
                }
            });
        }
    }
};
