<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات البائع
requireSeller();

$pageTitle = 'طلباتي';
$currentUser = getCurrentUser();
$message = '';
$messageType = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    $orderId = (int)($_POST['order_id'] ?? 0);
    
    // التحقق من ملكية الطلب
    if ($orderId > 0) {
        $stmt = $db->prepare("SELECT seller_id FROM orders WHERE id = ?");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch();
        
        if (!$order || $order['seller_id'] != $currentUser['id']) {
            $message = 'غير مسموح لك بالوصول إلى هذا الطلب';
            $messageType = 'error';
            $orderId = 0; // منع تنفيذ الإجراء
        }
    }
    
    if ($orderId > 0) {
        if ($action === 'update_status') {
            $newStatus = $_POST['new_status'] ?? '';
            $trackingNumber = sanitizeInput($_POST['tracking_number'] ?? '');
            $shippingCompany = sanitizeInput($_POST['shipping_company'] ?? '');
            
            // البائع يمكنه تحديث حالات معينة فقط
            $allowedStatuses = ['processing', 'shipped'];
            
            if (in_array($newStatus, $allowedStatuses)) {
                try {
                    $updateFields = ['status = ?', 'updated_at = NOW()'];
                    $params = [$newStatus];
                    
                    if ($newStatus === 'shipped' && $trackingNumber) {
                        $updateFields[] = 'tracking_number = ?';
                        $params[] = $trackingNumber;
                    }
                    
                    if ($shippingCompany) {
                        $updateFields[] = 'shipping_company = ?';
                        $params[] = $shippingCompany;
                    }
                    
                    $params[] = $orderId;
                    
                    $stmt = $db->prepare("UPDATE orders SET " . implode(', ', $updateFields) . " WHERE id = ?");
                    $stmt->execute($params);
                    
                    $message = 'تم تحديث حالة الطلب بنجاح';
                    $messageType = 'success';
                } catch (PDOException $e) {
                    $message = 'حدث خطأ في تحديث حالة الطلب';
                    $messageType = 'error';
                }
            } else {
                $message = 'حالة الطلب غير صحيحة';
                $messageType = 'error';
            }
        } elseif ($action === 'add_notes') {
            $notes = sanitizeInput($_POST['notes'] ?? '');
            
            try {
                $stmt = $db->prepare("UPDATE orders SET notes = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$notes, $orderId]);
                $message = 'تم إضافة الملاحظات بنجاح';
                $messageType = 'success';
            } catch (PDOException $e) {
                $message = 'حدث خطأ في إضافة الملاحظات';
                $messageType = 'error';
            }
        }
    }
}

// فلترة الطلبات
$statusFilter = $_GET['status'] ?? '';
$searchQuery = $_GET['search'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';

$whereConditions = ["o.seller_id = ?"];
$params = [$currentUser['id']];

if ($statusFilter) {
    $whereConditions[] = "o.status = ?";
    $params[] = $statusFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(o.order_number LIKE ? OR o.customer_name LIKE ? OR o.customer_email LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

if ($dateFrom) {
    $whereConditions[] = "DATE(o.created_at) >= ?";
    $params[] = $dateFrom;
}

if ($dateTo) {
    $whereConditions[] = "DATE(o.created_at) <= ?";
    $params[] = $dateTo;
}

$whereClause = implode(' AND ', $whereConditions);

// جلب طلبات البائع
try {
    $stmt = $db->prepare("
        SELECT o.*, COUNT(oi.id) as items_count,
               GROUP_CONCAT(CONCAT(p.name, ' (', oi.quantity, ')') SEPARATOR ', ') as product_details
        FROM orders o 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE $whereClause 
        GROUP BY o.id
        ORDER BY o.created_at DESC
    ");
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
} catch (PDOException $e) {
    $orders = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="https://via.placeholder.com/80x80?text=<?php echo substr($currentUser['full_name'], 0, 1); ?>" 
                         class="rounded-circle mb-2" alt="Seller">
                    <h6 class="text-white"><?php echo htmlspecialchars($currentUser['full_name']); ?></h6>
                    <small class="text-white-50">بائع</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-bag me-2"></i>المنتجات المتاحة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my-products.php">
                            <i class="fas fa-boxes me-2"></i>منتجاتي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">طلباتي</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="?status=pending" class="btn btn-sm btn-outline-warning">
                            <i class="fas fa-clock me-1"></i>المعلقة
                        </a>
                        <a href="?status=processing" class="btn btn-sm btn-outline-info">
                            <i class="fas fa-cog me-1"></i>قيد المعالجة
                        </a>
                        <a href="?status=shipped" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-shipping-fast me-1"></i>المشحونة
                        </a>
                    </div>
                </div>
            </div>

            <?php if ($message): ?>
                <?php echo showMessage($message, $messageType); ?>
            <?php endif; ?>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($searchQuery); ?>" 
                                   placeholder="رقم الطلب أو العميل">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>في الانتظار</option>
                                <option value="confirmed" <?php echo $statusFilter === 'confirmed' ? 'selected' : ''; ?>>مؤكد</option>
                                <option value="processing" <?php echo $statusFilter === 'processing' ? 'selected' : ''; ?>>قيد المعالجة</option>
                                <option value="shipped" <?php echo $statusFilter === 'shipped' ? 'selected' : ''; ?>>تم الشحن</option>
                                <option value="delivered" <?php echo $statusFilter === 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                                <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($dateFrom); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($dateTo); ?>">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search me-1"></i>بحث
                                </button>
                                <a href="orders.php" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-times me-1"></i>مسح
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Orders List -->
            <?php if (!empty($orders)): ?>
                <div class="row">
                    <?php foreach ($orders as $order): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">طلب #<?php echo htmlspecialchars($order['order_number']); ?></h6>
                                    <span class="badge bg-<?php 
                                        echo $order['status'] === 'delivered' ? 'success' : 
                                            ($order['status'] === 'pending' ? 'warning' : 
                                            ($order['status'] === 'cancelled' ? 'danger' : 'info')); 
                                    ?>">
                                        <?php echo getOrderStatusText($order['status']); ?>
                                    </span>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>العميل:</strong><br>
                                        <?php echo htmlspecialchars($order['customer_name']); ?><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($order['customer_email']); ?></small>
                                        <?php if ($order['customer_phone']): ?>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($order['customer_phone']); ?></small>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <strong>المنتجات:</strong><br>
                                        <small class="text-muted">
                                            <?php echo htmlspecialchars($order['product_details'] ?? 'لا توجد تفاصيل'); ?>
                                        </small>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <strong>المبلغ الإجمالي:</strong><br>
                                            <span class="text-primary"><?php echo formatPrice($order['total_amount']); ?></span>
                                        </div>
                                        <div class="col-6">
                                            <strong>العمولة:</strong><br>
                                            <span class="text-success"><?php echo formatPrice($order['commission_amount']); ?></span>
                                        </div>
                                    </div>
                                    
                                    <?php if ($order['tracking_number']): ?>
                                        <div class="mb-3">
                                            <strong>رقم التتبع:</strong><br>
                                            <code><?php echo htmlspecialchars($order['tracking_number']); ?></code>
                                            <?php if ($order['shipping_company']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($order['shipping_company']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            تاريخ الطلب: <?php echo formatDate($order['created_at']); ?>
                                            <?php if ($order['updated_at'] !== $order['created_at']): ?>
                                                <br>آخر تحديث: <?php echo formatDate($order['updated_at']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    
                                    <?php if ($order['notes']): ?>
                                        <div class="mb-3">
                                            <strong>ملاحظات:</strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($order['notes']); ?></small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex gap-2">
                                        <?php if (in_array($order['status'], ['pending', 'confirmed'])): ?>
                                            <button type="button" class="btn btn-sm btn-info" 
                                                    onclick="updateOrderStatus(<?php echo $order['id']; ?>, 'processing')">
                                                <i class="fas fa-cog me-1"></i>بدء المعالجة
                                            </button>
                                        <?php endif; ?>
                                        
                                        <?php if ($order['status'] === 'processing'): ?>
                                            <button type="button" class="btn btn-sm btn-primary" 
                                                    onclick="showShippingModal(<?php echo $order['id']; ?>)">
                                                <i class="fas fa-shipping-fast me-1"></i>تم الشحن
                                            </button>
                                        <?php endif; ?>
                                        
                                        <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                onclick="showNotesModal(<?php echo $order['id']; ?>, '<?php echo htmlspecialchars($order['notes'] ?? '', ENT_QUOTES); ?>')">
                                            <i class="fas fa-sticky-note me-1"></i>ملاحظات
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد طلبات</h5>
                    <p class="text-muted">لم يتم العثور على طلبات بالمعايير المحددة</p>
                    <a href="products.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>إضافة منتجات لاستقبال الطلبات
                    </a>
                </div>
            <?php endif; ?>
        </main>
    </div>
</div>

<!-- Shipping Modal -->
<div class="modal fade" id="shippingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تحديث معلومات الشحن</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="shippingForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="order_id" id="shippingOrderId">
                    <input type="hidden" name="new_status" value="shipped">
                    
                    <div class="mb-3">
                        <label for="tracking_number" class="form-label">رقم التتبع *</label>
                        <input type="text" class="form-control" id="tracking_number" name="tracking_number" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="shipping_company" class="form-label">شركة الشحن</label>
                        <input type="text" class="form-control" id="shipping_company" name="shipping_company" 
                               placeholder="مثال: أرامكس، DHL، فيدكس">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Notes Modal -->
<div class="modal fade" id="notesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة ملاحظات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="notesForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_notes">
                    <input type="hidden" name="order_id" id="notesOrderId">
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">الملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="4" 
                                  placeholder="أضف ملاحظات حول الطلب..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ الملاحظات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function updateOrderStatus(orderId, status) {
    if (confirm('هل أنت متأكد من تحديث حالة الطلب؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="update_status">
            <input type="hidden" name="order_id" value="${orderId}">
            <input type="hidden" name="new_status" value="${status}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function showShippingModal(orderId) {
    document.getElementById('shippingOrderId').value = orderId;
    document.getElementById('tracking_number').value = '';
    document.getElementById('shipping_company').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('shippingModal'));
    modal.show();
}

function showNotesModal(orderId, currentNotes) {
    document.getElementById('notesOrderId').value = orderId;
    document.getElementById('notes').value = currentNotes;
    
    const modal = new bootstrap.Modal(document.getElementById('notesModal'));
    modal.show();
}
</script>

<?php include '../includes/footer.php'; ?>
