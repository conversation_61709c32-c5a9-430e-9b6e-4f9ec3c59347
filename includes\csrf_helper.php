<?php
/**
 * مساعد CSRF Token المحسن
 * نظام بسيط وفعال لحماية CSRF
 */

// منع الوصول المباشر للملف
if (!defined('SECURITY_CHECK')) {
    die('Access Denied');
}

/**
 * توليد CSRF Token بسيط وفعال
 */
function generateSimpleCSRFToken() {
    if (!isset($_SESSION['simple_csrf_token']) || empty($_SESSION['simple_csrf_token'])) {
        $_SESSION['simple_csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }

    return $_SESSION['simple_csrf_token'];
}

/**
 * التحقق من CSRF Token البسيط
 */
function validateSimpleCSRFToken($token) {
    if (!isset($_SESSION['simple_csrf_token']) || empty($_SESSION['simple_csrf_token'])) {
        return false;
    }

    if (empty($token)) {
        return false;
    }

    return hash_equals($_SESSION['simple_csrf_token'], $token);
}

/**
 * إنشاء حقل CSRF مخفي للنماذج
 */
function getCSRFField() {
    $token = generateSimpleCSRFToken();
    return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token, ENT_QUOTES, 'UTF-8') . '">';
}

/**
 * التحقق من CSRF في بداية معالجة النموذج
 */
function checkCSRF() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        if (!validateSimpleCSRFToken($token)) {
            return false;
        }
    }
    return true;
}

/**
 * إعادة تعيين CSRF Token
 */
function resetCSRFToken() {
    unset($_SESSION['simple_csrf_token']);
    unset($_SESSION['csrf_token_time']);
}

/**
 * دالة مساعدة للحصول على رمز CSRF للاستخدام في JavaScript
 */
function getCSRFTokenForJS() {
    return generateSimpleCSRFToken();
}

/**
 * التحقق من CSRF مع رسالة خطأ مخصصة
 */
function validateCSRFWithMessage($token, &$errors) {
    // إذا لم يكن هناك رمز في الجلسة، إنشاء واحد جديد
    if (!isset($_SESSION['simple_csrf_token'])) {
        generateSimpleCSRFToken();
    }

    if (!validateSimpleCSRFToken($token)) {
        // لا نضيف رسالة خطأ، فقط نعيد false
        return false;
    }
    return true;
}
?>
