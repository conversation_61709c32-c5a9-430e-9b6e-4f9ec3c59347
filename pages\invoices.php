<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات البائع
requireSeller();

$pageTitle = 'فواتيري';
$currentUser = getCurrentUser();
$message = '';
$messageType = '';

// فلترة الفواتير
$statusFilter = $_GET['status'] ?? '';
$typeFilter = $_GET['type'] ?? '';
$searchQuery = $_GET['search'] ?? '';

$whereConditions = ["seller_id = ?"];
$params = [$currentUser['id']];

if ($statusFilter) {
    $whereConditions[] = "status = ?";
    $params[] = $statusFilter;
}

if ($typeFilter) {
    $whereConditions[] = "type = ?";
    $params[] = $typeFilter;
}

if ($searchQuery) {
    $whereConditions[] = "(invoice_number LIKE ? OR description LIKE ?)";
    $searchTerm = "%$searchQuery%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

// جلب الفواتير
try {
    $whereClause = implode(' AND ', $whereConditions);
    $stmt = $db->prepare("
        SELECT i.*, u.full_name as created_by_name 
        FROM invoices i 
        LEFT JOIN users u ON i.created_by = u.id 
        WHERE $whereClause 
        ORDER BY i.created_at DESC
    ");
    $stmt->execute($params);
    $invoices = $stmt->fetchAll();
} catch (PDOException $e) {
    $invoices = [];
    $message = 'حدث خطأ في جلب الفواتير';
    $messageType = 'error';
}

// حساب الإحصائيات
$totalAmount = 0;
$paidAmount = 0;
$pendingAmount = 0;

foreach ($invoices as $invoice) {
    $totalAmount += $invoice['amount'];
    if ($invoice['status'] === 'paid') {
        $paidAmount += $invoice['amount'];
    } elseif ($invoice['status'] === 'pending') {
        $pendingAmount += $invoice['amount'];
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <h5 class="text-primary">مرحباً <?php echo htmlspecialchars($currentUser['full_name']); ?></h5>
                    <small class="text-muted">بائع</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-bag me-2"></i>المنتجات المتاحة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my-products.php">
                            <i class="fas fa-boxes me-2"></i>منتجاتي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link text-danger" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">فواتيري</h1>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-<?php echo $messageType === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- إحصائيات الفواتير -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">إجمالي الفواتير</h5>
                            <h3 class="text-primary"><?php echo number_format($totalAmount, 2); ?> ر.س</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">المدفوع</h5>
                            <h3 class="text-success"><?php echo number_format($paidAmount, 2); ?> ر.س</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">المعلق</h5>
                            <h3 class="text-warning"><?php echo number_format($pendingAmount, 2); ?> ر.س</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">البحث</label>
                            <input type="text" class="form-control" name="search" value="<?php echo htmlspecialchars($searchQuery); ?>" placeholder="رقم الفاتورة أو الوصف">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">الحالة</label>
                            <select class="form-select" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>معلقة</option>
                                <option value="paid" <?php echo $statusFilter === 'paid' ? 'selected' : ''; ?>>مدفوعة</option>
                                <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>ملغاة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">النوع</label>
                            <select class="form-select" name="type">
                                <option value="">جميع الأنواع</option>
                                <option value="commission" <?php echo $typeFilter === 'commission' ? 'selected' : ''; ?>>عمولة</option>
                                <option value="payout" <?php echo $typeFilter === 'payout' ? 'selected' : ''; ?>>دفعة</option>
                                <option value="other" <?php echo $typeFilter === 'other' ? 'selected' : ''; ?>>أخرى</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary">بحث</button>
                                <a href="invoices.php" class="btn btn-outline-secondary">إعادة تعيين</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول الفواتير -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة الفواتير (<?php echo count($invoices); ?>)</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($invoices)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد فواتير حالياً</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>تاريخ الإصدار</th>
                                        <th>تاريخ الاستحقاق</th>
                                        <th>الحالة</th>
                                        <th>الوصف</th>
                                        <th>أنشأها</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($invoices as $invoice): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                            </td>
                                            <td>
                                                <?php
                                                $typeLabels = [
                                                    'commission' => 'عمولة',
                                                    'payout' => 'دفعة',
                                                    'other' => 'أخرى'
                                                ];
                                                echo $typeLabels[$invoice['type']] ?? $invoice['type'];
                                                ?>
                                            </td>
                                            <td>
                                                <strong class="text-primary"><?php echo number_format($invoice['amount'], 2); ?> ر.س</strong>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($invoice['issue_date'])); ?></td>
                                            <td>
                                                <?php if ($invoice['due_date']): ?>
                                                    <?php echo date('Y-m-d', strtotime($invoice['due_date'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">غير محدد</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClasses = [
                                                    'pending' => 'warning',
                                                    'paid' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                $statusLabels = [
                                                    'pending' => 'معلقة',
                                                    'paid' => 'مدفوعة',
                                                    'cancelled' => 'ملغاة'
                                                ];
                                                $statusClass = $statusClasses[$invoice['status']] ?? 'secondary';
                                                $statusLabel = $statusLabels[$invoice['status']] ?? $invoice['status'];
                                                ?>
                                                <span class="badge bg-<?php echo $statusClass; ?>"><?php echo $statusLabel; ?></span>
                                            </td>
                                            <td>
                                                <?php if ($invoice['description']): ?>
                                                    <?php echo htmlspecialchars(substr($invoice['description'], 0, 50)); ?>
                                                    <?php if (strlen($invoice['description']) > 50): ?>...<?php endif; ?>
                                                <?php else: ?>
                                                    <span class="text-muted">لا يوجد وصف</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($invoice['created_by_name'] ?? 'غير معروف'); ?>
                                                </small>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
