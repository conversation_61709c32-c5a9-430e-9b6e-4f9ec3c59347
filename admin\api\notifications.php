<?php
/**
 * API لجلب الإشعارات
 */

// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../../includes/functions.php';

// التحقق من الصلاحيات
if (!isUserLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح']);
    exit;
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['error' => 'ليس لديك صلاحية']);
    exit;
}

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// الحصول على نوع الإشعار
$type = $_GET['type'] ?? 'all';

try {
    switch ($type) {
        case 'orders':
            $notifications = getOrdersNotifications();
            break;
        case 'users':
            $notifications = getUsersNotifications();
            break;
        case 'counts':
            $notifications = getNotificationCounts();
            break;
        default:
            $notifications = getAllNotifications();
            break;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $notifications
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في الخادم'
    ]);
}

// دالة جلب إشعارات الطلبات
function getOrdersNotifications() {
    global $db;
    
    try {
        $stmt = $db->prepare("
            SELECT o.*, u.full_name as seller_name, u.store_name
            FROM orders o 
            JOIN users u ON o.seller_id = u.id 
            WHERE o.status = 'pending' 
            ORDER BY o.created_at DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $orders = $stmt->fetchAll();
        
        $notifications = [];
        foreach ($orders as $order) {
            $notifications[] = [
                'id' => $order['id'],
                'type' => 'order',
                'title' => 'طلب جديد #' . $order['order_number'],
                'description' => 'طلب من البائع: ' . $order['seller_name'],
                'time' => timeAgo($order['created_at']),
                'unread' => true,
                'link' => 'orders.php?id=' . $order['id']
            ];
        }
        
        return $notifications;
        
    } catch (PDOException $e) {
        return [];
    }
}

// دالة جلب إشعارات البائعين
function getUsersNotifications() {
    global $db;
    
    try {
        $stmt = $db->prepare("
            SELECT * FROM users 
            WHERE role = 'seller' AND status = 'pending' 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        $notifications = [];
        foreach ($users as $user) {
            $notifications[] = [
                'id' => $user['id'],
                'type' => 'user',
                'title' => 'بائع جديد',
                'description' => $user['full_name'] . ' يطلب الانضمام كبائع',
                'time' => timeAgo($user['created_at']),
                'unread' => true,
                'link' => 'users.php?id=' . $user['id']
            ];
        }
        
        return $notifications;
        
    } catch (PDOException $e) {
        return [];
    }
}

// دالة جلب عدادات الإشعارات
function getNotificationCounts() {
    global $db;
    
    $counts = [
        'orders' => 0,
        'users' => 0,
        'general' => 3 // إشعارات عامة ثابتة
    ];
    
    try {
        // عدد الطلبات المعلقة
        $stmt = $db->prepare("SELECT COUNT(*) FROM orders WHERE status = 'pending'");
        $stmt->execute();
        $counts['orders'] = $stmt->fetchColumn();
        
        // عدد البائعين المعلقين
        $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE role = 'seller' AND status = 'pending'");
        $stmt->execute();
        $counts['users'] = $stmt->fetchColumn();
        
    } catch (PDOException $e) {
        // في حالة الخطأ، نعيد القيم الافتراضية
    }
    
    return $counts;
}

// دالة جلب جميع الإشعارات
function getAllNotifications() {
    $orders = getOrdersNotifications();
    $users = getUsersNotifications();
    
    // دمج الإشعارات وترتيبها حسب الوقت
    $all = array_merge($orders, $users);
    
    // إضافة إشعارات عامة
    $general = [
        [
            'id' => 'sys_1',
            'type' => 'system',
            'title' => 'تحديث النظام',
            'description' => 'تم تحديث لوحة التحكم بنجاح',
            'time' => 'منذ 5 دقائق',
            'unread' => true,
            'link' => '#'
        ],
        [
            'id' => 'sys_2',
            'type' => 'security',
            'title' => 'فحص أمني',
            'description' => 'تم إجراء فحص أمني شامل للنظام',
            'time' => 'منذ ساعة',
            'unread' => false,
            'link' => 'security.php'
        ]
    ];
    
    $all = array_merge($all, $general);
    
    return array_slice($all, 0, 20); // أحدث 20 إشعار
}

// دالة حساب الوقت المنقضي
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) {
        return 'منذ ' . $time . ' ثانية';
    } elseif ($time < 3600) {
        return 'منذ ' . floor($time / 60) . ' دقيقة';
    } elseif ($time < 86400) {
        return 'منذ ' . floor($time / 3600) . ' ساعة';
    } elseif ($time < 2592000) {
        return 'منذ ' . floor($time / 86400) . ' يوم';
    } elseif ($time < 31536000) {
        return 'منذ ' . floor($time / 2592000) . ' شهر';
    } else {
        return 'منذ ' . floor($time / 31536000) . ' سنة';
    }
}
?>
