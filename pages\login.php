<?php
// بدء الجلسة
session_start();

// تضمين قاعدة البيانات
require_once '../config/database.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
    if (isset($_SESSION['user_role'])) {
        if ($_SESSION['user_role'] === 'admin') {
            header('Location: ../admin/index.php');
            exit();
        } else {
            header('Location: ../pages/dashboard.php');
            exit();
        }
    }
}

$pageTitle = 'تسجيل الدخول';
$errors = [];
$success = '';

// دوال مساعدة بسيطة
function cleanInput($data) {
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var($_POST['email'] ?? '', FILTER_SANITIZE_EMAIL);
    $password = $_POST['password'] ?? '';

    // التحقق من البيانات
    if (empty($email)) {
        $errors[] = 'البريد الإلكتروني مطلوب';
    } elseif (!isValidEmail($email)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }

    if (empty($password)) {
        $errors[] = 'كلمة المرور مطلوبة';
    }

    // محاولة تسجيل الدخول
    if (empty($errors)) {
        try {
            $stmt = $db->prepare("SELECT id, email, password, full_name, role, status FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($user && password_verify($password, $user['password'])) {
                // التحقق من حالة الحساب
                if ($user['status'] === 'rejected') {
                    $errors[] = 'تم رفض طلب التسجيل الخاص بك. يرجى التواصل مع الإدارة.';
                } elseif ($user['status'] === 'suspended') {
                    $errors[] = 'تم تعليق حسابك. يرجى التواصل مع الإدارة.';
                } elseif ($user['status'] === 'pending') {
                    $errors[] = 'حسابك قيد المراجعة. سيتم تفعيل حسابك خلال 24-48 ساعة.';
                } elseif ($user['status'] === 'active') {
                    // تسجيل دخول ناجح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_email'] = $user['email'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['user_status'] = $user['status'];

                    // تحديث آخر تسجيل دخول
                    try {
                        $updateStmt = $db->prepare("UPDATE users SET last_login = NOW(), ip_address = ? WHERE id = ?");
                        $updateStmt->execute([$_SERVER['REMOTE_ADDR'], $user['id']]);
                    } catch (PDOException $e) {
                        // تجاهل خطأ التحديث
                    }

                    // إعادة التوجيه حسب نوع المستخدم
                    if ($user['role'] === 'admin') {
                        header('Location: ../admin/index.php');
                        exit();
                    } else {
                        header('Location: ../pages/dashboard.php');
                        exit();
                    }
                } else {
                    $errors[] = 'حالة الحساب غير صحيحة. يرجى التواصل مع الإدارة.';
                }
            } else {
                $errors[] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
            }
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى.';
        }
    }
}


?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?> - منصة البائعين</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }

        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .login-header h2 {
            margin: 0;
            font-weight: 600;
        }

        .login-body {
            padding: 40px;
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            padding: 15px;
            font-weight: 600;
            transition: transform 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(255,255,255,0.3);
            color: white;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }
    </style>
</head>
<body>

<a href="../index.php" class="back-link">
    <i class="fas fa-arrow-right me-2"></i>العودة للرئيسية
</a>

<div class="login-container">
    <div class="login-header">
        <h2><i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول</h2>
        <p class="mb-0 mt-2">أدخل بياناتك للوصول إلى حسابك</p>
    </div>

    <div class="login-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate autocomplete="off">

                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo cleanInput($email ?? ''); ?>"
                                   required>
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="password" class="form-label">كلمة المرور *</label>
                            <input type="password" class="form-control" id="password" name="password"
                                   required>
                            <div class="invalid-feedback">
                                يرجى إدخال كلمة المرور
                            </div>
                        </div>

                        <div class="d-grid gap-2 mb-4">
                            <button type="submit" class="btn btn-login btn-lg text-white">
                                <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                            </button>
                        </div>
                    </form>

                    <div class="text-center">
                        <p class="text-muted">ليس لديك حساب؟ <a href="register.php" class="text-decoration-none fw-bold">إنشاء حساب جديد</a></p>
                    </div>

                    <div class="text-center mt-3">
                        <small class="text-muted">
                            <strong>للاختبار:</strong><br>
                            البريد: <EMAIL><br>
                            كلمة المرور: password
                        </small>
                    </div>
                </div>
            </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>


// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

</body>
</html>
