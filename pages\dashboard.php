<?php
require_once '../includes/functions.php';

// التحقق من صلاحيات البائع
requireSeller();

$pageTitle = 'لوحة تحكم البائع';
$currentUser = getCurrentUser();

// جلب إحصائيات البائع
$stats = getSellerStats($currentUser['id']);

// جلب آخر الطلبات
try {
    $stmt = $db->prepare("
        SELECT o.*, COUNT(oi.id) as items_count
        FROM orders o 
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.seller_id = ? 
        GROUP BY o.id
        ORDER BY o.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$currentUser['id']]);
    $recentOrders = $stmt->fetchAll();
} catch (PDOException $e) {
    $recentOrders = [];
}

// جلب المنتجات الأكثر مبيعاً
try {
    $stmt = $db->prepare("
        SELECT p.name, p.image_url, COUNT(oi.id) as sales_count, SUM(oi.quantity) as total_quantity
        FROM products p
        JOIN order_items oi ON p.id = oi.product_id
        JOIN orders o ON oi.order_id = o.id
        WHERE o.seller_id = ?
        GROUP BY p.id
        ORDER BY sales_count DESC
        LIMIT 5
    ");
    $stmt->execute([$currentUser['id']]);
    $topProducts = $stmt->fetchAll();
} catch (PDOException $e) {
    $topProducts = [];
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="text-center mb-4">
                    <img src="https://via.placeholder.com/80x80?text=<?php echo substr($currentUser['full_name'], 0, 1); ?>" 
                         class="rounded-circle mb-2" alt="Seller">
                    <h6 class="text-white"><?php echo htmlspecialchars($currentUser['full_name']); ?></h6>
                    <small class="text-white-50">بائع</small>
                </div>
                
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="products.php">
                            <i class="fas fa-shopping-bag me-2"></i>المنتجات المتاحة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="my-products.php">
                            <i class="fas fa-boxes me-2"></i>منتجاتي
                            <span class="badge bg-info"><?php echo $stats['products'] ?? 0; ?></span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="orders.php">
                            <i class="fas fa-shopping-cart me-2"></i>الطلبات
                            <?php if (($stats['orders']['pending'] ?? 0) > 0): ?>
                                <span class="badge bg-warning"><?php echo $stats['orders']['pending']; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invoices.php">
                            <i class="fas fa-file-invoice me-2"></i>الفواتير
                            <?php if (($stats['invoices']['pending'] ?? 0) > 0): ?>
                                <span class="badge bg-secondary"><?php echo $stats['invoices']['pending']; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="profile.php">
                            <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                        </a>
                    </li>
                    <li class="nav-item mt-3">
                        <a class="nav-link" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">مرحباً، <?php echo htmlspecialchars($currentUser['full_name']); ?></h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-calendar me-1"></i>
                            <span id="current-time"></span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card bg-primary">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3><?php echo $stats['products'] ?? 0; ?></h3>
                                <p class="mb-0">منتجاتي</p>
                                <small>المنتجات المضافة</small>
                            </div>
                            <i class="fas fa-boxes fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stats-card bg-success">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3><?php echo $stats['orders']['total'] ?? 0; ?></h3>
                                <p class="mb-0">إجمالي الطلبات</p>
                                <small><?php echo $stats['orders']['completed'] ?? 0; ?> مكتمل</small>
                            </div>
                            <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stats-card bg-warning">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3><?php echo formatPrice($stats['orders']['total_sales'] ?? 0); ?></h3>
                                <p class="mb-0">إجمالي المبيعات</p>
                                <small>المبلغ الإجمالي</small>
                            </div>
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="stats-card bg-info">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3><?php echo formatPrice($stats['orders']['total_commission'] ?? 0); ?></h3>
                                <p class="mb-0">إجمالي العمولة</p>
                                <small>أرباحي</small>
                            </div>
                            <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-shopping-cart me-2"></i>آخر الطلبات</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentOrders)): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recentOrders as $order): ?>
                                                <tr>
                                                    <td>
                                                        <a href="order-details.php?id=<?php echo $order['id']; ?>">
                                                            #<?php echo htmlspecialchars($order['order_number']); ?>
                                                        </a>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                                    <td><?php echo formatPrice($order['total_amount']); ?></td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $order['status'] === 'delivered' ? 'success' : ($order['status'] === 'pending' ? 'warning' : 'info'); ?>">
                                                            <?php echo getOrderStatusText($order['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo formatDate($order['created_at']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-center mt-3">
                                    <a href="orders.php" class="btn btn-primary btn-sm">عرض جميع الطلبات</a>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">لا توجد طلبات بعد</h6>
                                    <p class="text-muted">ابدأ بإضافة منتجات لمتجرك لاستقبال الطلبات</p>
                                    <a href="products.php" class="btn btn-primary">تصفح المنتجات</a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-star me-2"></i>المنتجات الأكثر مبيعاً</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($topProducts)): ?>
                                <div class="list-group list-group-flush">
                                    <?php foreach ($topProducts as $product): ?>
                                        <div class="list-group-item d-flex align-items-center">
                                            <img src="<?php echo htmlspecialchars($product['image_url']); ?>" 
                                                 class="rounded me-3" width="50" height="50" alt="Product">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1"><?php echo htmlspecialchars(substr($product['name'], 0, 30)) . '...'; ?></h6>
                                                <small class="text-muted">
                                                    <?php echo $product['sales_count']; ?> طلب | 
                                                    <?php echo $product['total_quantity']; ?> قطعة
                                                </small>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-3">
                                    <i class="fas fa-chart-bar fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">لا توجد مبيعات بعد</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-bolt me-2"></i>إجراءات سريعة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <a href="products.php" class="btn btn-success w-100 mb-2">
                                        <i class="fas fa-plus me-2"></i>إضافة منتجات جديدة
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="my-products.php" class="btn btn-primary w-100 mb-2">
                                        <i class="fas fa-edit me-2"></i>إدارة منتجاتي
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="orders.php?status=pending" class="btn btn-warning w-100 mb-2">
                                        <i class="fas fa-clock me-2"></i>الطلبات المعلقة
                                    </a>
                                </div>
                                <div class="col-md-3">
                                    <a href="profile.php" class="btn btn-secondary w-100 mb-2">
                                        <i class="fas fa-user-edit me-2"></i>تحديث الملف الشخصي
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tips and Notifications -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-lightbulb me-2"></i>نصائح لزيادة المبيعات</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                                        <div>
                                            <h6>اختر منتجات متنوعة</h6>
                                            <small class="text-muted">أضف منتجات من فئات مختلفة لجذب عملاء أكثر</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                                        <div>
                                            <h6>حدد أسعار تنافسية</h6>
                                            <small class="text-muted">ادرس السوق وحدد أسعار مناسبة لمنتجاتك</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="d-flex align-items-start">
                                        <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                                        <div>
                                            <h6>تابع طلباتك باستمرار</h6>
                                            <small class="text-muted">استجب سريعاً للطلبات وحدث حالتها</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
