<?php
/**
 * ملف الدوال الأساسية - إصدار محسن
 * تم إصلاح مشكلة الحلقة اللا نهائية
 */

// منع الوصول المباشر للملف
if (!defined('SECURITY_CHECK')) {
    die('Access Denied');
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/security.php';
require_once __DIR__ . '/advanced_security.php';
require_once __DIR__ . '/simple_csrf.php';

// وظيفة تنظيف البيانات
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// وظيفة تنظيف البيانات المتقدمة
function sanitizeInputAdvanced($input) {
    if (is_array($input)) {
        return array_map('sanitizeInputAdvanced', $input);
    }

    // إزالة المسافات الزائدة
    $input = trim($input);

    // إزالة الشرطات المائلة
    $input = stripslashes($input);

    // استخدام دوال الحماية المتقدمة
    if (function_exists('sanitizeXSS')) {
        $input = sanitizeXSS($input);
    }
    if (function_exists('sanitizeSQL')) {
        $input = sanitizeSQL($input);
    }

    return $input;
}

// وظيفة التحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// وظيفة إنشاء رمز جلسة عشوائي
function generateSessionToken() {
    return bin2hex(random_bytes(32));
}

// وظيفة التحقق من تسجيل دخول المستخدم (مبسطة)
function isUserLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// وظيفة التحقق من تسجيل دخول البائع
function isSellerLoggedIn() {
    return isUserLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'seller';
}

// وظيفة التحقق من تسجيل دخول المدير
function isAdminLoggedIn() {
    return isUserLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

// وظيفة الحصول على معلومات المستخدم الحالي
function getCurrentUser() {
    global $db;

    if (!isUserLoggedIn()) {
        return false;
    }

    try {
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        return false;
    }
}

// وظيفة تسجيل خروج المستخدم (مبسطة)
function logoutUser() {
    // مسح جميع متغيرات الجلسة
    $_SESSION = array();

    // حذف ملف تعريف الارتباط للجلسة
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }

    // تدمير الجلسة
    session_destroy();
}

// وظيفة إعادة التوجيه
function redirect($url) {
    header("Location: " . $url);
    exit();
}

// وظيفة التحقق من الصلاحيات
function requireLogin() {
    if (!isUserLoggedIn()) {
        redirect('../pages/login.php');
    }
}

function requireAdmin() {
    requireLogin();
    if (!isAdminLoggedIn()) {
        redirect('../pages/dashboard.php');
    }
}

// وظيفة عرض الرسائل
function showMessage($message, $type = 'info') {
    $alertClass = '';
    switch ($type) {
        case 'success':
            $alertClass = 'alert-success';
            break;
        case 'error':
            $alertClass = 'alert-danger';
            break;
        case 'warning':
            $alertClass = 'alert-warning';
            break;
        default:
            $alertClass = 'alert-info';
    }
    
    return '<div class="alert ' . $alertClass . ' alert-dismissible fade show" role="alert">
                ' . htmlspecialchars($message) . '
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>';
}

// وظيفة تنسيق السعر
function formatPrice($price) {
    return number_format($price, 2) . ' د.م';
}

// وظيفة تنسيق التاريخ
function formatDate($date) {
    return date('d/m/Y H:i', strtotime($date));
}

// وظيفة إنشاء رقم طلب فريد
function generateOrderNumber() {
    return 'ORD-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));
}

?>