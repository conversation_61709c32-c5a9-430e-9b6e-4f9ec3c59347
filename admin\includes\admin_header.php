<?php
/**
 * ملف الرأس للوحة تحكم المدير
 * يحتوي على الشريط العلوي والجانبي
 */

// التأكد من أن المستخدم مدير
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('Location: ../pages/login.php');
    exit();
}

// الحصول على بيانات المستخدم الحالي
$user = [
    'full_name' => $_SESSION['user_name'] ?? 'المدير',
    'email' => $_SESSION['user_email'] ?? '',
    'role' => $_SESSION['user_role'] ?? 'admin'
];

// تحديد الصفحة النشطة
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle ?? 'لوحة تحكم المدير'); ?> - منصة البائعين</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Admin Dashboard CSS -->
    <link href="assets/css/admin-dashboard.css" rel="stylesheet">
    
    <!-- إضافة رؤوس الأمان -->
    <?php
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    ?>
    
    <!-- CSS إضافي خاص بالصفحة -->
    <?php if (isset($additional_css)): ?>
        <style><?php echo $additional_css; ?></style>
    <?php endif; ?>
</head>
<body>

<!-- الشريط العلوي -->
<nav class="top-navbar">
    <div class="d-flex align-items-center">
        <button class="sidebar-toggle" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
        <a href="index.php" class="navbar-brand ms-3">
            <i class="fas fa-tachometer-alt me-2"></i>
            لوحة تحكم المدير
        </a>
    </div>
    
    <div class="d-flex align-items-center gap-3">
        <div class="current-time">
            <small class="text-white-50">الوقت الحالي</small>
            <div id="current-time" class="fw-bold"></div>
        </div>
        
        <!-- أيقونات الإشعارات -->
        <div class="navbar-notifications">
            <!-- إشعارات الطلبات الجديدة -->
            <div class="position-relative">
                <a href="orders.php?status=pending" class="notification-icon" title="الطلبات الجديدة">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="notification-badge" id="ordersBadge">0</span>
                </a>
            </div>
            
            <!-- إشعارات البائعين الجدد -->
            <div class="position-relative">
                <a href="users.php?status=pending" class="notification-icon" title="البائعين الجدد">
                    <i class="fas fa-user-plus"></i>
                    <span class="notification-badge" id="usersBadge">0</span>
                </a>
            </div>
        </div>
        
        <div class="position-relative">
            <div class="user-profile" id="userProfileToggle">
                <div class="user-info">
                    <div class="user-name"><?php echo htmlspecialchars($user['full_name']); ?></div>
                    <div class="user-role">مدير النظام</div>
                </div>
                <div class="user-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="user-dropdown-arrow">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
            
            <!-- قائمة المستخدم المنسدلة -->
            <div class="user-dropdown" id="userDropdown">
                <div class="user-dropdown-header">
                    <div class="user-dropdown-avatar">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="user-dropdown-info">
                        <div class="user-dropdown-name"><?php echo htmlspecialchars($user['full_name']); ?></div>
                        <div class="user-dropdown-role">مدير النظام</div>
                        <div class="user-dropdown-email"><?php echo htmlspecialchars($user['email']); ?></div>
                    </div>
                </div>
                
                <div class="user-dropdown-menu">
                    <a href="profile.php" class="user-dropdown-item">
                        <i class="fas fa-user me-2"></i>
                        الملف الشخصي
                    </a>
                    <div class="user-dropdown-divider"></div>
                    <a href="../pages/logout.php" class="user-dropdown-item logout">
                        <i class="fas fa-sign-out-alt me-2"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- الشريط الجانبي -->
<aside class="sidebar" id="sidebar">
    <ul class="sidebar-menu">
        <li>
            <a href="index.php" class="<?php echo ($current_page === 'index.php') ? 'active' : ''; ?>">
                <i class="fas fa-home"></i>
                <span>الرئيسية</span>
            </a>
        </li>
        <li>
            <a href="users.php" class="<?php echo ($current_page === 'users.php') ? 'active' : ''; ?>">
                <i class="fas fa-users"></i>
                <span>إدارة البائعين</span>
            </a>
        </li>
        <li>
            <a href="products.php" class="<?php echo ($current_page === 'products.php') ? 'active' : ''; ?>">
                <i class="fas fa-box"></i>
                <span>إدارة المنتجات</span>
            </a>
        </li>
        <li>
            <a href="orders.php" class="<?php echo ($current_page === 'orders.php') ? 'active' : ''; ?>">
                <i class="fas fa-shopping-cart"></i>
                <span>إدارة الطلبات</span>
            </a>
        </li>
        <li>
            <a href="categories.php" class="<?php echo ($current_page === 'categories.php') ? 'active' : ''; ?>">
                <i class="fas fa-tags"></i>
                <span>إدارة الفئات</span>
            </a>
        </li>
        <li>
            <a href="invoices.php" class="<?php echo ($current_page === 'invoices.php') ? 'active' : ''; ?>">
                <i class="fas fa-file-invoice"></i>
                <span>إدارة الفواتير</span>
            </a>
        </li>
        <li>
            <a href="reports.php" class="<?php echo ($current_page === 'reports.php') ? 'active' : ''; ?>">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
        </li>
        <li>
            <a href="settings.php" class="<?php echo ($current_page === 'settings.php') ? 'active' : ''; ?>">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </li>
        <li>
            <a href="security.php" class="<?php echo ($current_page === 'security.php') ? 'active' : ''; ?>">
                <i class="fas fa-shield-alt"></i>
                <span>الأمان والحماية</span>
            </a>
        </li>
    </ul>
</aside>

<!-- المحتوى الرئيسي -->
<main class="main-content" id="mainContent">
    <div class="container-fluid">
