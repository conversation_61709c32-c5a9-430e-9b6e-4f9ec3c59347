<?php
// بدء الجلسة الآمنة
if (session_status() === PHP_SESSION_NONE) {
    if (!headers_sent()) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            ini_set('session.cookie_secure', 1);
        }
    }
    session_start();
}

require_once '../includes/functions.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isUserLoggedIn()) {
    redirect('../pages/login.php');
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect('../pages/dashboard.php');
}

$pageTitle = 'التقارير والإحصائيات';

// الحصول على الإحصائيات
$stats = getAdminStats();

// تحديد الفترة الزمنية للتقرير
$period = $_GET['period'] ?? 'month';
$startDate = '';
$endDate = '';

switch ($period) {
    case 'today':
        $startDate = date('Y-m-d');
        $endDate = date('Y-m-d');
        break;
    case 'week':
        $startDate = date('Y-m-d', strtotime('-7 days'));
        $endDate = date('Y-m-d');
        break;
    case 'month':
        $startDate = date('Y-m-01');
        $endDate = date('Y-m-t');
        break;
    case 'year':
        $startDate = date('Y-01-01');
        $endDate = date('Y-12-31');
        break;
    case 'custom':
        $startDate = $_GET['start_date'] ?? date('Y-m-01');
        $endDate = $_GET['end_date'] ?? date('Y-m-t');
        break;
}

// جلب إحصائيات المبيعات
try {
    $stmt = $db->prepare("
        SELECT 
            COUNT(*) as total_orders,
            SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as total_sales,
            SUM(CASE WHEN status = 'pending' THEN total_amount ELSE 0 END) as pending_sales,
            AVG(CASE WHEN status = 'completed' THEN total_amount ELSE NULL END) as avg_order_value
        FROM orders 
        WHERE DATE(created_at) BETWEEN ? AND ?
    ");
    $stmt->execute([$startDate, $endDate]);
    $salesStats = $stmt->fetch();
} catch (PDOException $e) {
    $salesStats = ['total_orders' => 0, 'total_sales' => 0, 'pending_sales' => 0, 'avg_order_value' => 0];
}

// جلب أفضل البائعين
try {
    $stmt = $db->prepare("
        SELECT 
            u.full_name,
            u.store_name,
            COUNT(o.id) as order_count,
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales
        FROM users u
        LEFT JOIN orders o ON u.id = o.seller_id AND DATE(o.created_at) BETWEEN ? AND ?
        WHERE u.role = 'seller'
        GROUP BY u.id
        ORDER BY total_sales DESC
        LIMIT 10
    ");
    $stmt->execute([$startDate, $endDate]);
    $topSellers = $stmt->fetchAll();
} catch (PDOException $e) {
    $topSellers = [];
}

// جلب أفضل المنتجات
try {
    $stmt = $db->prepare("
        SELECT 
            p.name,
            p.category,
            COUNT(oi.id) as order_count,
            SUM(oi.quantity) as total_quantity,
            SUM(oi.price * oi.quantity) as total_revenue
        FROM products p
        LEFT JOIN order_items oi ON p.id = oi.product_id
        LEFT JOIN orders o ON oi.order_id = o.id AND DATE(o.created_at) BETWEEN ? AND ?
        GROUP BY p.id
        HAVING order_count > 0
        ORDER BY total_revenue DESC
        LIMIT 10
    ");
    $stmt->execute([$startDate, $endDate]);
    $topProducts = $stmt->fetchAll();
} catch (PDOException $e) {
    $topProducts = [];
}

// جلب إحصائيات الفئات
try {
    $stmt = $db->prepare("
        SELECT 
            c.name as category_name,
            COUNT(p.id) as product_count,
            COUNT(oi.id) as order_count,
            SUM(oi.quantity) as total_quantity
        FROM categories c
        LEFT JOIN products p ON c.name = p.category
        LEFT JOIN order_items oi ON p.id = oi.product_id
        LEFT JOIN orders o ON oi.order_id = o.id AND DATE(o.created_at) BETWEEN ? AND ?
        GROUP BY c.id
        ORDER BY order_count DESC
    ");
    $stmt->execute([$startDate, $endDate]);
    $categoryStats = $stmt->fetchAll();
} catch (PDOException $e) {
    $categoryStats = [];
}

// جلب إحصائيات يومية للرسم البياني
try {
    $stmt = $db->prepare("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as order_count,
            SUM(CASE WHEN status = 'completed' THEN total_amount ELSE 0 END) as daily_sales
        FROM orders 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    $stmt->execute([$startDate, $endDate]);
    $dailyStats = $stmt->fetchAll();
} catch (PDOException $e) {
    $dailyStats = [];
}

// CSS إضافي خاص بهذه الصفحة
$additional_css = '
/* تخصيصات إضافية خاصة بصفحة التقارير */
.report-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.period-selector {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
}

.table-responsive {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar-custom {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.export-buttons {
    margin-bottom: 20px;
}

.top-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.top-item:last-child {
    border-bottom: none;
}

.rank-badge {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    margin-left: 10px;
}

.rank-1 { background: #ffd700; }
.rank-2 { background: #c0c0c0; }
.rank-3 { background: #cd7f32; }
.rank-other { background: #6c757d; }
';

// JavaScript إضافي خاص بهذه الصفحة
$additional_js = '
// رسم الرسوم البيانية
function drawCharts() {
    // بيانات المبيعات اليومية
    const dailyData = ' . json_encode($dailyStats) . ';
    
    if (dailyData.length > 0) {
        const ctx = document.getElementById("salesChart");
        if (ctx) {
            new Chart(ctx, {
                type: "line",
                data: {
                    labels: dailyData.map(d => d.date),
                    datasets: [{
                        label: "المبيعات اليومية",
                        data: dailyData.map(d => parseFloat(d.daily_sales)),
                        borderColor: "#667eea",
                        backgroundColor: "rgba(102, 126, 234, 0.1)",
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }
}

// تصدير التقارير
function exportReport(format) {
    const params = new URLSearchParams(window.location.search);
    params.set("export", format);
    window.location.href = "export-report.php?" + params.toString();
}

// تحديث الفترة الزمنية
function updatePeriod() {
    const period = document.getElementById("period").value;
    const customDates = document.getElementById("customDates");
    
    if (period === "custom") {
        customDates.style.display = "block";
    } else {
        customDates.style.display = "none";
        // إعادة تحميل الصفحة مع الفترة الجديدة
        window.location.href = "reports.php?period=" + period;
    }
}

// تحميل الرسوم البيانية عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", function() {
    // تحقق من وجود Chart.js
    if (typeof Chart !== "undefined") {
        drawCharts();
    } else {
        // تحميل Chart.js إذا لم يكن موجود
        const script = document.createElement("script");
        script.src = "https://cdn.jsdelivr.net/npm/chart.js";
        script.onload = drawCharts;
        document.head.appendChild(script);
    }
});
';

// تضمين header المدير
include 'includes/admin_header.php';
?>

        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="page-title">التقارير والإحصائيات</h1>
            <div class="export-buttons">
                <button type="button" class="btn btn-success btn-sm" onclick="exportReport('excel')">
                    <i class="fas fa-file-excel me-1"></i>تصدير Excel
                </button>
                <button type="button" class="btn btn-danger btn-sm" onclick="exportReport('pdf')">
                    <i class="fas fa-file-pdf me-1"></i>تصدير PDF
                </button>
            </div>
        </div>

        <!-- اختيار الفترة الزمنية -->
        <div class="period-selector">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-3">
                    <label class="form-label">الفترة الزمنية</label>
                    <select class="form-control" id="period" name="period" onchange="updatePeriod()">
                        <option value="today" <?php echo $period === 'today' ? 'selected' : ''; ?>>اليوم</option>
                        <option value="week" <?php echo $period === 'week' ? 'selected' : ''; ?>>آخر 7 أيام</option>
                        <option value="month" <?php echo $period === 'month' ? 'selected' : ''; ?>>هذا الشهر</option>
                        <option value="year" <?php echo $period === 'year' ? 'selected' : ''; ?>>هذا العام</option>
                        <option value="custom" <?php echo $period === 'custom' ? 'selected' : ''; ?>>فترة مخصصة</option>
                    </select>
                </div>
                
                <div id="customDates" class="col-md-6" style="display: <?php echo $period === 'custom' ? 'block' : 'none'; ?>;">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" name="start_date" value="<?php echo $startDate; ?>">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" name="end_date" value="<?php echo $endDate; ?>">
                        </div>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">تطبيق</button>
                </div>
            </form>
        </div>

        <!-- الإحصائيات الرئيسية -->
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($salesStats['total_orders']); ?></div>
                    <div class="stat-label">إجمالي الطلبات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($salesStats['total_sales'], 2); ?></div>
                    <div class="stat-label">إجمالي المبيعات (د.م)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($salesStats['pending_sales'], 2); ?></div>
                    <div class="stat-label">مبيعات معلقة (د.م)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($salesStats['avg_order_value'], 2); ?></div>
                    <div class="stat-label">متوسط قيمة الطلب (د.م)</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- الرسم البياني للمبيعات -->
            <div class="col-md-8">
                <div class="report-card">
                    <h5 class="mb-4"><i class="fas fa-chart-line me-2"></i>تطور المبيعات</h5>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- أفضل البائعين -->
            <div class="col-md-4">
                <div class="report-card">
                    <h5 class="mb-4"><i class="fas fa-trophy me-2"></i>أفضل البائعين</h5>
                    <?php if (!empty($topSellers)): ?>
                        <?php foreach (array_slice($topSellers, 0, 5) as $index => $seller): ?>
                            <div class="top-item">
                                <div class="d-flex align-items-center">
                                    <div class="rank-badge rank-<?php echo $index < 3 ? $index + 1 : 'other'; ?>">
                                        <?php echo $index + 1; ?>
                                    </div>
                                    <div>
                                        <strong><?php echo htmlspecialchars($seller['full_name']); ?></strong><br>
                                        <small class="text-muted"><?php echo htmlspecialchars($seller['store_name']); ?></small>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <strong><?php echo number_format($seller['total_sales'], 2); ?> د.م</strong><br>
                                    <small class="text-muted"><?php echo $seller['order_count']; ?> طلب</small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد بيانات للفترة المحددة</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- أفضل المنتجات -->
            <div class="col-md-6">
                <div class="report-card">
                    <h5 class="mb-4"><i class="fas fa-star me-2"></i>أفضل المنتجات</h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>الإيرادات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($topProducts)): ?>
                                    <?php foreach (array_slice($topProducts, 0, 5) as $product): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($product['name']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($product['category']); ?></small>
                                            </td>
                                            <td><?php echo $product['total_quantity']; ?></td>
                                            <td><?php echo number_format($product['total_revenue'], 2); ?> د.م</td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center text-muted">لا توجد بيانات</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- إحصائيات الفئات -->
            <div class="col-md-6">
                <div class="report-card">
                    <h5 class="mb-4"><i class="fas fa-tags me-2"></i>أداء الفئات</h5>
                    <?php if (!empty($categoryStats)): ?>
                        <?php
                        $maxOrders = max(array_column($categoryStats, 'order_count'));
                        foreach (array_slice($categoryStats, 0, 5) as $category):
                            $percentage = $maxOrders > 0 ? ($category['order_count'] / $maxOrders) * 100 : 0;
                        ?>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <span><?php echo htmlspecialchars($category['category_name']); ?></span>
                                    <small class="text-muted"><?php echo $category['order_count']; ?> طلب</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar progress-bar-custom"
                                         style="width: <?php echo $percentage; ?>%"></div>
                                </div>
                                <small class="text-muted"><?php echo $category['product_count']; ?> منتج</small>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted text-center">لا توجد بيانات للفترة المحددة</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- تقارير إضافية -->
        <div class="row">
            <div class="col-md-12">
                <div class="report-card">
                    <h5 class="mb-4"><i class="fas fa-chart-bar me-2"></i>تقارير مفصلة</h5>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h6>تقرير البائعين</h6>
                                <p class="text-muted small">تفاصيل أداء جميع البائعين</p>
                                <a href="reports.php?type=sellers&period=<?php echo $period; ?>" class="btn btn-sm btn-outline-primary">عرض</a>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-box fa-2x text-success mb-2"></i>
                                <h6>تقرير المنتجات</h6>
                                <p class="text-muted small">تحليل أداء المنتجات والمخزون</p>
                                <a href="reports.php?type=products&period=<?php echo $period; ?>" class="btn btn-sm btn-outline-success">عرض</a>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-shopping-cart fa-2x text-info mb-2"></i>
                                <h6>تقرير الطلبات</h6>
                                <p class="text-muted small">تحليل مفصل لجميع الطلبات</p>
                                <a href="reports.php?type=orders&period=<?php echo $period; ?>" class="btn btn-sm btn-outline-info">عرض</a>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="text-center p-3 border rounded">
                                <i class="fas fa-money-bill-wave fa-2x text-warning mb-2"></i>
                                <h6>تقرير الأرباح</h6>
                                <p class="text-muted small">تحليل الأرباح والعمولات</p>
                                <a href="reports.php?type=profits&period=<?php echo $period; ?>" class="btn btn-sm btn-outline-warning">عرض</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ملخص الفترة -->
        <div class="row">
            <div class="col-md-12">
                <div class="report-card">
                    <h5 class="mb-4"><i class="fas fa-calendar me-2"></i>ملخص الفترة</h5>
                    <div class="alert alert-info">
                        <strong>الفترة المحددة:</strong>
                        من <?php echo date('d/m/Y', strtotime($startDate)); ?>
                        إلى <?php echo date('d/m/Y', strtotime($endDate)); ?>

                        <div class="mt-2">
                            <small>
                                • تم تسجيل <?php echo number_format($salesStats['total_orders']); ?> طلب بقيمة إجمالية <?php echo number_format($salesStats['total_sales'], 2); ?> د.م<br>
                                • يوجد <?php echo count($topSellers); ?> بائع نشط في هذه الفترة<br>
                                • تم بيع <?php echo count($topProducts); ?> منتج مختلف<br>
                                • متوسط قيمة الطلب الواحد: <?php echo number_format($salesStats['avg_order_value'], 2); ?> د.م
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

<?php
// تضمين footer المدير
include 'includes/admin_footer.php';
?>
