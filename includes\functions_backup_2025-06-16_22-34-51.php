<?php
// ملف دوال مبسط - إصلاح طارئ
if (!defined('SECURITY_CHECK')) {
    die('Access Denied');
}

// تضمين ملفات قاعدة البيانات والحماية
require_once __DIR__ . '/../config/database.php';

// دوال أساسية مبسطة
function sanitizeInput($data) {
    return htmlspecialchars(trim(stripslashes($data)), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function isUserLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function isAdminLoggedIn() {
    return isUserLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

function logoutUser() {
    $_SESSION = array();
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000, $params["path"], $params["domain"], $params["secure"], $params["httponly"]);
    }
    session_destroy();
}

function redirect($url) {
    header("Location: " . $url);
    exit();
}

function requireLogin() {
    if (!isUserLoggedIn()) {
        redirect('../pages/login.php');
    }
}

function requireAdmin() {
    requireLogin();
    if (!isAdminLoggedIn()) {
        redirect('../pages/dashboard.php');
    }
}

function showMessage($message, $type = 'info') {
    $alertClass = $type === 'success' ? 'alert-success' : ($type === 'error' ? 'alert-danger' : 'alert-info');
    return '<div class="alert ' . $alertClass . '">' . htmlspecialchars($message) . '</div>';
}

function formatPrice($price) {
    return number_format($price, 2) . ' د.م';
}

function formatDate($date) {
    return date('d/m/Y H:i', strtotime($date));
}
?>